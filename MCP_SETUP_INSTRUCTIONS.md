# MCP Servers Setup Instructions

## Installed MCP Servers

The following MCP servers have been configured for your SpherosegV4 project:

### 1. Filesystem Server
- **Purpose**: Secure file operations within project directories
- **Access**: All packages directories (`frontend`, `backend`, `ml`, `shared`, `types`)
- **Configuration**: Pre-configured with all your project paths

### 2. Memory Server  
- **Purpose**: Persistent context across Claude sessions
- **Benefit**: Remembers previous conversations and project context

### 3. Docker MCP Server
- **Purpose**: Natural language Docker container management
- **Features**: Container, image, volume, and network operations
- **Perfect for**: Managing your docker-compose services

### 4. PostgreSQL MCP Server
- **Purpose**: Direct database access and query execution
- **Configuration**: Connected to your spheroseg database (localhost:5432)
- **Credentials**: postgres/postgres (as per your setup)

### 5. VS Code MCP Server
- **Purpose**: IDE integration and diagnostics
- **Features**: Workspace structure, linter problems, language server diagnostics

### 6. Code Sandbox MCP
- **Purpose**: Secure code execution in isolated containers
- **Features**: Multi-language support (Python, Node.js, TypeScript)
- **Benefit**: Safe testing without affecting your main environment

### 7. Sequential Thinking Server
- **Purpose**: Enhanced problem-solving through structured thought processes
- **Benefit**: Better debugging and complex task handling

## How to Use

### Claude Code CLI & Desktop
The MCP servers have been automatically configured in your Claude Desktop configuration:
`/home/<USER>/.config/Claude/claude_desktop_config.json`

**To use with Claude Code CLI:**
1. Restart Claude Code CLI if it's currently running
2. Use `/mcp` command to see available MCP servers
3. The servers should now be visible and functional

**To verify the configuration:**
```bash
cat /home/<USER>/.config/Claude/claude_desktop_config.json
```

## Configuration File Location

The complete configuration is saved in:
`/home/<USER>/spheroseg/mcp-servers-config.json`

## Useful Commands for Your Project

With these MCP servers, you can now:

- **File Operations**: "Read the frontend package.json" or "Edit the backend API routes"
- **Docker Management**: "Show running containers" or "Restart the ml service"
- **Database Queries**: "Show all tables in spheroseg database" or "Query user data"
- **Code Execution**: "Run this Python script safely" or "Test this TypeScript function"
- **Development**: "Check for linting errors" or "Show project diagnostics"

## Notes

- The filesystem server is configured with access to your entire project structure
- Docker MCP will work with your existing docker-compose setup
- PostgreSQL MCP is pre-configured for your database connection
- Code sandbox MCP provides safe execution environment for testing

## Troubleshooting

If you encounter issues:
1. Ensure all packages are properly installed via npm/uvx
2. Check that database is running: `docker-compose ps`
3. Verify Docker daemon is running for Docker MCP
4. Node.js version warnings can be ignored for most MCP servers