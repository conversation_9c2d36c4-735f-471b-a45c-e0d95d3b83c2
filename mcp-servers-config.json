{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/spheroseg/spheroseg", "/home/<USER>/spheroseg/spheroseg/packages", "/home/<USER>/spheroseg/spheroseg/packages/frontend", "/home/<USER>/spheroseg/spheroseg/packages/backend", "/home/<USER>/spheroseg/spheroseg/packages/ml", "/home/<USER>/spheroseg/spheroseg/packages/shared", "/home/<USER>/spheroseg/spheroseg/packages/types"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "docker": {"command": "uvx", "args": ["mcp-server-docker"]}, "postgres": {"command": "npx", "args": ["-y", "postgres-mcp-server"], "env": {"DATABASE_URL": "postgresql://postgres:postgres@localhost:5432/spheroseg"}}, "vscode": {"command": "npx", "args": ["-y", "vscode-mcp-server"]}, "code-sandbox": {"command": "/home/<USER>/.local/share/code-sandbox-mcp/code-sandbox-mcp", "args": [], "env": {}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequentialthinking"]}}}