/**
 * Worker utilities for polygon operations
 * Consolidated from frontend duplicates to shared package
 */
import { Point } from '@spheroseg/types';
import { WorkerRequest, WorkerResponse } from './geometryUtils';

/**
 * Interface for polygon worker operations
 */
export interface PolygonWorker {
  postMessage(message: WorkerRequest): void;
  onmessage: ((event: MessageEvent<WorkerResponse>) => void) | null;
  terminate(): void;
}

/**
 * Execute a polygon operation in a web worker
 */
export const executePolygonWorkerOperation = async (
  operation: string,
  data: any,
  worker?: PolygonWorker
): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (!worker) {
      reject(new Error('Worker not available'));
      return;
    }

    const requestId = `${Date.now()}-${Math.random()}`;
    const timeout = setTimeout(() => {
      reject(new Error('Worker operation timeout'));
    }, 10000);

    const handleMessage = (event: MessageEvent<WorkerResponse>) => {
      const { id, result, error } = event.data;
      
      if (id === requestId) {
        clearTimeout(timeout);
        if (worker.onmessage === handleMessage) {
          worker.onmessage = null;
        }
        
        if (error) {
          reject(new Error(error));
        } else {
          resolve(result);
        }
      }
    };

    worker.onmessage = handleMessage;
    worker.postMessage({
      id: requestId,
      operation,
      data
    });
  });
};

/**
 * Calculate polygon area asynchronously using worker
 */
export const calculatePolygonAreaAsync = async (
  points: Point[],
  worker?: PolygonWorker
): Promise<number> => {
  return executePolygonWorkerOperation('calculatePolygonArea', { points }, worker);
};

/**
 * Calculate polygon perimeter asynchronously using worker
 */
export const calculatePolygonPerimeterAsync = async (
  points: Point[],
  worker?: PolygonWorker
): Promise<number> => {
  return executePolygonWorkerOperation('calculatePolygonPerimeter', { points }, worker);
};

/**
 * Calculate bounding box asynchronously using worker
 */
export const calculateBoundingBoxAsync = async (
  points: Point[],
  worker?: PolygonWorker
): Promise<{ minX: number; minY: number; maxX: number; maxY: number }> => {
  return executePolygonWorkerOperation('calculateBoundingBox', { points }, worker);
};

/**
 * Simplify polygon asynchronously using worker
 */
export const simplifyPolygonAsync = async (
  points: Point[],
  epsilon: number,
  worker?: PolygonWorker
): Promise<Point[]> => {
  return executePolygonWorkerOperation('simplifyPolygon', { points, epsilon }, worker);
};

/**
 * Check if point is in polygon asynchronously using worker
 */
export const isPointInPolygonAsync = async (
  point: Point,
  polygon: Point[],
  worker?: PolygonWorker
): Promise<boolean> => {
  return executePolygonWorkerOperation('isPointInPolygon', { point, polygon }, worker);
};

/**
 * Slice polygon asynchronously using worker
 */
export const slicePolygonAsync = async (
  polygon: Point[],
  sliceStart: Point,
  sliceEnd: Point,
  worker?: PolygonWorker
): Promise<Point[][]> => {
  return executePolygonWorkerOperation('slicePolygon', { polygon, sliceStart, sliceEnd }, worker);
};
