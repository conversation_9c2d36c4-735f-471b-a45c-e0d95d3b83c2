<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Copy/Paste Detector Report</title><link href="styles/tailwind.css" rel="stylesheet"><link href="styles/prism.css" rel="stylesheet"></head><body class="bg-gray-100"><header class="bg-white shadow py-4"><div class="container mx-auto px-4"><h1 class="text-3xl font-semibold text-gray-800">jscpd - copy/paste report</h1></div></header><main class="container mx-auto my-8 p-4 bg-white shadow rounded"><section class="mb-8" id="dashboard"><h2 class="text-2xl font-semibold text-gray-700 mb-4">Dashboard</h2><div class="grid grid-cols-4 gap-4"><div class="bg-blue-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-blue-800 mb-2">Total Files</h3><span class="text-4xl font-bold text-blue-800">711</span></div><div class="bg-green-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-green-800 mb-2">Total Lines of Code</h3><span class="text-4xl font-bold text-green-800">91504</span></div><div class="bg-yellow-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-yellow-800 mb-2">Number of Clones</h3><span class="text-4xl font-bold text-yellow-800">191</span></div><div class="bg-red-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-red-800 mb-2">Duplicated Lines</h3><span class="text-4xl font-bold text-red-800">3753 (4.10%)</span></div></div></section><section class="mb-8" id="formats"><h2 class="text-2xl font-semibold text-gray-700 mb-4">Formats with Duplications</h2><table class="w-full table-auto"><thead><tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal"><th class="py-3 px-6 text-left">Format</th><th class="py-3 px-6 text-left">Files</th><th class="py-3 px-6 text-left">Lines</th><th class="py-3 px-6 text-left">Clones</th><th class="py-3 px-6 text-left">Duplicated Lines</th><th class="py-3 px-6 text-left">Duplicated Tokens</th></tr></thead><tbody><tr class="bg-white border-b border-gray-200 text-gray-800 text-sm"><td class="py-3 px-6"><a class="text-blue-600 hover:underline" href="#typescript-clones">typescript</a></td><td class="py-3 px-6">242</td><td class="py-3 px-6">46120</td><td class="py-3 px-6">115</td><td class="py-3 px-6">2460</td><td class="py-3 px-6">22751</td></tr><tr class="bg-white border-b border-gray-200 text-gray-800 text-sm"><td class="py-3 px-6"><a class="text-blue-600 hover:underline" href="#tsx-clones">tsx</a></td><td class="py-3 px-6">257</td><td class="py-3 px-6">33878</td><td class="py-3 px-6">57</td><td class="py-3 px-6">776</td><td class="py-3 px-6">6144</td></tr><tr class="bg-white border-b border-gray-200 text-gray-800 text-sm"><td class="py-3 px-6"><a class="text-blue-600 hover:underline" href="#javascript-clones">javascript</a></td><td class="py-3 px-6">211</td><td class="py-3 px-6">11499</td><td class="py-3 px-6">19</td><td class="py-3 px-6">517</td><td class="py-3 px-6">4223</td></tr><tr class="bg-white border-b border-gray-200 text-gray-800 text-sm"><td class="py-3 px-6"><a class="text-blue-600 hover:underline" href="#css-clones">css</a></td><td class="py-3 px-6">1</td><td class="py-3 px-6">7</td><td class="py-3 px-6">0</td><td class="py-3 px-6">0</td><td class="py-3 px-6">0</td></tr></tbody></table></section><section class="mb-8" id="txt-clones"><a name="typescript-clones"></a><h2 class="text-2xl font-semibold text-gray-700 mb-4">typescript</h2><div class="divide-y divide-gray-200 border-b-2"><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/hooks/segmentation/api.ts (Line 293:20 - Line 312:2), packages/frontend/src/pages/segmentation/hooks/segmentation/api.ts (Line 269:12 - Line 288:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn0" onclick="toggleCodeBlock('cloneGroup0', 'expandBtn0', 'collapseBtn0')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn0" onclick="toggleCodeBlock('cloneGroup0', 'expandBtn0', 'collapseBtn0')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup0"><code class="language-typescript text-sm text-gray-800">.polygons
          .filter((polygon: Polygon) =&gt; polygon &amp;&amp; Array.isArray(polygon.points) &amp;&amp; polygon.points.length &gt;= 3)
          .map((polygon: Polygon) =&gt; ({
            ...polygon,
            id: polygon.id || `poly-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            type: polygon.type || 'external',
            points: polygon.points.map((p: any) =&gt; {
              // Handle both {x,y} and [x,y] formats
              if (Array.isArray(p)) {
                return { x: p[0], y: p[1] };
              } else if (typeof p === 'object' &amp;&amp; 'x' in p &amp;&amp; 'y' in p) {
                return { x: p.x, y: p.y };
              }
              return p;
            }),
          }));

        logger.info(`Processed ${processedPolygons.length} valid polygons for imageId=${imageId}`);
        fetchedSegmentation.polygons = processedPolygons;
      } else if (fetchedSegmentation &amp;&amp; !</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/hooks/useSegmentationKeyboard.ts (Line 117:6 - Line 128:12), packages/frontend/src/pages/segmentation/hooks/segmentation/useSegmentationV2.ts (Line 443:5 - Line 456:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn13" onclick="toggleCodeBlock('cloneGroup13', 'expandBtn13', 'collapseBtn13')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn13" onclick="toggleCodeBlock('cloneGroup13', 'expandBtn13', 'collapseBtn13')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup13"><code class="language-typescript text-sm text-gray-800">);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () =&gt; {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [editMode, setEditMode</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/hooks/useOptimizedSegmentation.ts (Line 72:5 - Line 79:17), packages/frontend/src/pages/segmentation/hooks/segmentation/useSegmentationV2.ts (Line 51:5 - Line 58:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn14" onclick="toggleCodeBlock('cloneGroup14', 'expandBtn14', 'collapseBtn14')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn14" onclick="toggleCodeBlock('cloneGroup14', 'expandBtn14', 'collapseBtn14')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup14"><code class="language-typescript text-sm text-gray-800">);
  const [selectedPolygonId, setSelectedPolygonId] = useState&lt;string | null&gt;(null);
  const [hoveredVertex, setHoveredVertex] = useState&lt;{
    polygonId: string;
    vertexIndex: number;
  } | null&gt;(null);
  const [tempPoints, setTempPoints] = useState&lt;Point[]&gt;([]);
  const [interactionState</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/hooks/useOptimizedSegmentation.ts (Line 87:2 - Line 93:26), packages/frontend/src/pages/segmentation/hooks/segmentation/useSegmentationV2.ts (Line 30:5 - Line 36:74)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn15" onclick="toggleCodeBlock('cloneGroup15', 'expandBtn15', 'collapseBtn15')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn15" onclick="toggleCodeBlock('cloneGroup15', 'expandBtn15', 'collapseBtn15')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup15"><code class="language-typescript text-sm text-gray-800">);
  const [isLoading, setIsLoading] = useState&lt;boolean&gt;(true);
  const [isSaving, setIsSaving] = useState&lt;boolean&gt;(false);
  const [isResegmenting, setIsResegmenting] = useState&lt;boolean&gt;(false);
  const [error, setError] = useState&lt;string | null&gt;(null);

  // Historie pro Undo/Redo</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/hooks/useOptimizedSegmentation.ts (Line 182:6 - Line 193:18), packages/frontend/src/pages/segmentation/hooks/segmentation/useSegmentationV2.ts (Line 443:2 - Line 455:9)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn16" onclick="toggleCodeBlock('cloneGroup16', 'expandBtn16', 'collapseBtn16')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn16" onclick="toggleCodeBlock('cloneGroup16', 'expandBtn16', 'collapseBtn16')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup16"><code class="language-typescript text-sm text-gray-800">;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () =&gt; {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [selectedPolygonId</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/lib/__mocks__/enhanced/apiClient.ts (Line 223:2 - Line 232:5), packages/frontend/src/pages/segmentation/__tests__/utils/testUtils.ts (Line 84:2 - Line 93:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn17" onclick="toggleCodeBlock('cloneGroup17', 'expandBtn17', 'collapseBtn17')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn17" onclick="toggleCodeBlock('cloneGroup17', 'expandBtn17', 'collapseBtn17')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup17"><code class="language-typescript text-sm text-gray-800">[
    {
      id: 'polygon-1',
      points: [
        { x: 100, y: 100 },
        { x: 200, y: 100 },
        { x: 200, y: 200 },
        { x: 100, y: 200 },
      ],
      type</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/lib/__mocks__/enhanced/apiClient.ts (Line 232:11 - Line 244:5), packages/frontend/src/pages/segmentation/__tests__/utils/testUtils.ts (Line 92:2 - Line 104:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn18" onclick="toggleCodeBlock('cloneGroup18', 'expandBtn18', 'collapseBtn18')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn18" onclick="toggleCodeBlock('cloneGroup18', 'expandBtn18', 'collapseBtn18')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup18"><code class="language-typescript text-sm text-gray-800">,
      color: '#FF0000',
      label: 'Cell 1',
    },
    {
      id: 'polygon-2',
      points: [
        { x: 300, y: 300 },
        { x: 400, y: 300 },
        { x: 400, y: 400 },
        { x: 300, y: 400 },
      ],
      type</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/geometry/geometryUtils.ts (Line 45:3 - Line 52:2), packages/frontend/src/pages/segmentation/utils/geometry.ts (Line 7:3 - Line 14:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn19" onclick="toggleCodeBlock('cloneGroup19', 'expandBtn19', 'collapseBtn19')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn19" onclick="toggleCodeBlock('cloneGroup19', 'expandBtn19', 'collapseBtn19')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup19"><code class="language-typescript text-sm text-gray-800">let inside = false;
  for (let i = 0, j = polygon.length - 1; i &lt; polygon.length; j = i++) {
    const xi = polygon[i].x;
    const yi = polygon[i].y;
    const xj = polygon[j].x;
    const yj = polygon[j].y;

    const intersect = (</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/geometry/geometryUtils.ts (Line 205:2 - Line 214:2), packages/frontend/src/pages/segmentation/hooks/polygonInteraction/geometry/utils/intersectionUtils.ts (Line 80:2 - Line 87:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn20" onclick="toggleCodeBlock('cloneGroup20', 'expandBtn20', 'collapseBtn20')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn20" onclick="toggleCodeBlock('cloneGroup20', 'expandBtn20', 'collapseBtn20')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup20"><code class="language-typescript text-sm text-gray-800">(points: Point[]): number =&gt; {
  let area = 0;

  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    area += points[i].x * points[j].y;
    area -= points[j].x * points[i].y;
  }

  return Math.abs(area /</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/geometry/geometryUtils.ts (Line 231:10 - Line 254:2), packages/frontend/src/pages/segmentation/utils/geometry.ts (Line 42:2 - Line 65:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn21" onclick="toggleCodeBlock('cloneGroup21', 'expandBtn21', 'collapseBtn21')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn21" onclick="toggleCodeBlock('cloneGroup21', 'expandBtn21', 'collapseBtn21')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup21"><code class="language-typescript text-sm text-gray-800">;
};

/**
 * Determines if a polygon is oriented clockwise
 */
export const isClockwise = (points: Point[]): boolean =&gt; {
  let sum = 0;
  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    sum += (points[j].x - points[i].x) * (points[j].y + points[i].y);
  }
  return sum &gt; 0;
};

/**
 * Ensures a polygon's points are in clockwise order
 */
export const ensureClockwise = (points: Point[]): Point[] =&gt; {
  if (!isClockwise(points)) {
    return [...points].reverse();
  }
  return points;
};</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonSlicingUtils.ts (Line 39:11 - Line 53:11), packages/shared/src/utils/geometry/geometryUtils.ts (Line 75:3 - Line 89:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn22" onclick="toggleCodeBlock('cloneGroup22', 'expandBtn22', 'collapseBtn22')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn22" onclick="toggleCodeBlock('cloneGroup22', 'expandBtn22', 'collapseBtn22')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup22"><code class="language-typescript text-sm text-gray-800">.y;

  const determinant = a1 * b2 - a2 * b1;

  if (determinant === 0) {
    // Lines are parallel
    return null;
  }

  const x = (b2 * c1 - b1 * c2) / determinant;
  const y = (a1 * c2 - a2 * c1) / determinant;

  // Check if the intersection point is on both line segments
  const onSegment1 =
    Math.min(line1Start</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonSlicingUtils.ts (Line 74:1 - Line 108:4), packages/shared/src/utils/geometry/geometryUtils.ts (Line 139:1 - Line 190:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn23" onclick="toggleCodeBlock('cloneGroup23', 'expandBtn23', 'collapseBtn23')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn23" onclick="toggleCodeBlock('cloneGroup23', 'expandBtn23', 'collapseBtn23')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup23"><code class="language-typescript text-sm text-gray-800">export const distanceToLineSegment = (point: Point, lineStart: Point, lineEnd: Point): number =&gt; {
  const dx = lineEnd.x - lineStart.x;
  const dy = lineEnd.y - lineStart.y;

  // Line length squared
  const lineLengthSquared = dx * dx + dy * dy;

  if (lineLengthSquared === 0) {
    // Line is actually a point
    return Math.sqrt(Math.pow(point.x - lineStart.x, 2) + Math.pow(point.y - lineStart.y, 2));
  }

  // Calculate the projection of the point onto the line
  const t = ((point.x - lineStart.x) * dx + (point.y - lineStart.y) * dy) / lineLengthSquared;

  if (t &lt; 0) {
    // Point is beyond the lineStart end of the line segment
    return Math.sqrt(Math.pow(point.x - lineStart.x, 2) + Math.pow(point.y - lineStart.y, 2));
  }

  if (t &gt; 1) {
    // Point is beyond the lineEnd end of the line segment
    return Math.sqrt(Math.pow(point.x - lineEnd.x, 2) + Math.pow(point.y - lineEnd.y, 2));
  }

  // Projection falls on the line segment
  const projectionX = lineStart.x + t * dx;
  const projectionY = lineStart.y + t * dy;

  return Math.sqrt(Math.pow(point.x - projectionX, 2) + Math.pow(point.y - projectionY, 2));
};

/**
 * Create a new polygon with the given points and type
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 29:7 - Line 78:4), packages/shared/src/utils/geometry/geometryUtils.ts (Line 13:7 - Line 23:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn24" onclick="toggleCodeBlock('cloneGroup24', 'expandBtn24', 'collapseBtn24')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn24" onclick="toggleCodeBlock('cloneGroup24', 'expandBtn24', 'collapseBtn24')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup24"><code class="language-typescript text-sm text-gray-800">;
}

/**
 * Calculate the bounding box of a polygon
 */
export const calculateBoundingBox = (points: Point[]): BoundingBox =&gt; {
  if (!points.length) {
    return { minX: 0, minY: 0, maxX: 0, maxY: 0 };
  }

  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  for (const point of points) {
    minX = Math.min(minX, point.x);
    minY = Math.min(minY, point.y);
    maxX = Math.max(maxX, point.x);
    maxY = Math.max(maxY, point.y);
  }

  return { minX, minY, maxX, maxY };
};

/**
 * Check if a point is inside a polygon using ray casting algorithm
 */
export const isPointInPolygon = (point: Point, polygon: Point[]): boolean =&gt; {
  if (polygon.length &lt; 3) return false;

  let inside = false;
  for (let i = 0, j = polygon.length - 1; i &lt; polygon.length; j = i++) {
    const xi = polygon[i].x;
    const yi = polygon[i].y;
    const xj = polygon[j].x;
    const yj = polygon[j].y;

    const intersect = yi &gt; point.y !== yj &gt; point.y &amp;&amp; point.x &lt; ((xj - xi) * (point.y - yi)) / (yj - yi) + xi;

    if (intersect) inside = !inside;
  }

  return inside;
};

/**
 * Calculate the intersection point of two line segments
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 68:3 - Line 128:2), packages/shared/src/utils/geometry/geometryUtils.ts (Line 53:2 - Line 122:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn25" onclick="toggleCodeBlock('cloneGroup25', 'expandBtn25', 'collapseBtn25')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn25" onclick="toggleCodeBlock('cloneGroup25', 'expandBtn25', 'collapseBtn25')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup25"><code class="language-typescript text-sm text-gray-800">;

    if (intersect) inside = !inside;
  }

  return inside;
};

/**
 * Calculate the intersection point of two line segments
 */
export const calculateIntersection = (p1: Point, p2: Point, p3: Point, p4: Point): Point | null =&gt; {
  // Line 1 represented as a1x + b1y = c1
  const a1 = p2.y - p1.y;
  const b1 = p1.x - p2.x;
  const c1 = a1 * p1.x + b1 * p1.y;

  // Line 2 represented as a2x + b2y = c2
  const a2 = p4.y - p3.y;
  const b2 = p3.x - p4.x;
  const c2 = a2 * p3.x + b2 * p3.y;

  const determinant = a1 * b2 - a2 * b1;

  if (determinant === 0) {
    // Lines are parallel
    return null;
  }

  const x = (b2 * c1 - b1 * c2) / determinant;
  const y = (a1 * c2 - a2 * c1) / determinant;

  // Check if the intersection point is on both line segments
  const onSegment1 =
    Math.min(p1.x, p2.x) &lt;= x &amp;&amp; x &lt;= Math.max(p1.x, p2.x) &amp;&amp; Math.min(p1.y, p2.y) &lt;= y &amp;&amp; y &lt;= Math.max(p1.y, p2.y);

  const onSegment2 =
    Math.min(p3.x, p4.x) &lt;= x &amp;&amp; x &lt;= Math.max(p3.x, p4.x) &amp;&amp; Math.min(p3.y, p4.y) &lt;= y &amp;&amp; y &lt;= Math.max(p3.y, p4.y);

  if (onSegment1 &amp;&amp; onSegment2) {
    return { x, y };
  }

  return null;
};

/**
 * Calculate all intersection points between a line and a polygon
 */
export const calculateLinePolygonIntersections = (lineStart: Point, lineEnd: Point, polygon: Point[]): Point[] =&gt; {
  const intersections: Point[] = [];

  for (let i = 0; i &lt; polygon.length; i++) {
    const j = (i + 1) % polygon.length;
    const intersection = calculateIntersection(lineStart, lineEnd, polygon[i], polygon[j]);

    if (intersection) {
      // Add a small epsilon to avoid duplicate points
      const epsilon = 0.0001;
      const isDuplicate = intersections.some(
        (</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 148:3 - Line 177:4), packages/shared/src/utils/geometry/geometryUtils.ts (Line 148:3 - Line 190:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn26" onclick="toggleCodeBlock('cloneGroup26', 'expandBtn26', 'collapseBtn26')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn26" onclick="toggleCodeBlock('cloneGroup26', 'expandBtn26', 'collapseBtn26')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup26"><code class="language-typescript text-sm text-gray-800">const lineLengthSquared = dx * dx + dy * dy;

  if (lineLengthSquared === 0) {
    // Line is actually a point
    return Math.sqrt(Math.pow(point.x - lineStart.x, 2) + Math.pow(point.y - lineStart.y, 2));
  }

  // Calculate the projection of the point onto the line
  const t = ((point.x - lineStart.x) * dx + (point.y - lineStart.y) * dy) / lineLengthSquared;

  if (t &lt; 0) {
    // Point is beyond the lineStart end of the line segment
    return Math.sqrt(Math.pow(point.x - lineStart.x, 2) + Math.pow(point.y - lineStart.y, 2));
  }

  if (t &gt; 1) {
    // Point is beyond the lineEnd end of the line segment
    return Math.sqrt(Math.pow(point.x - lineEnd.x, 2) + Math.pow(point.y - lineEnd.y, 2));
  }

  // Projection falls on the line segment
  const projectionX = lineStart.x + t * dx;
  const projectionY = lineStart.y + t * dy;

  return Math.sqrt(Math.pow(point.x - projectionX, 2) + Math.pow(point.y - projectionY, 2));
};

/**
 * Slice a polygon with a line
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 175:1 - Line 249:4), packages/shared/src/utils/geometry/slicingUtils.ts (Line 20:1 - Line 102:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn27" onclick="toggleCodeBlock('cloneGroup27', 'expandBtn27', 'collapseBtn27')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn27" onclick="toggleCodeBlock('cloneGroup27', 'expandBtn27', 'collapseBtn27')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup27"><code class="language-typescript text-sm text-gray-800">/**
 * Slice a polygon with a line
 */
export const slicePolygon = (polygon: Point[], sliceStart: Point, sliceEnd: Point): Point[][] =&gt; {
  // Find intersections
  const intersections = calculateLinePolygonIntersections(sliceStart, sliceEnd, polygon);

  // If we don't have exactly 2 intersections, we can't slice properly
  if (intersections.length !== 2) {
    return [polygon]; // Return original polygon
  }

  // Sort intersections by distance from slice start
  intersections.sort((a, b) =&gt; {
    const distA = Math.pow(a.x - sliceStart.x, 2) + Math.pow(a.y - sliceStart.y, 2);
    const distB = Math.pow(b.x - sliceStart.x, 2) + Math.pow(b.y - sliceStart.y, 2);
    return distA - distB;
  });

  // Find the polygon edges that contain the intersections
  const intersectionEdges: number[] = [];

  for (const intersection of intersections) {
    for (let i = 0; i &lt; polygon.length; i++) {
      const j = (i + 1) % polygon.length;
      const p1 = polygon[i];
      const p2 = polygon[j];

      const intersection2 = calculateIntersection(sliceStart, sliceEnd, p1, p2);

      if (intersection2) {
        const epsilon = 0.0001;
        if (
          Math.abs(intersection.x - intersection2.x) &lt; epsilon &amp;&amp;
          Math.abs(intersection.y - intersection2.y) &lt; epsilon
        ) {
          intersectionEdges.push(i);
          break;
        }
      }
    }
  }

  // Create two new polygons
  const poly1: Point[] = [];
  const poly2: Point[] = [];

  // First polygon: from edge1 to edge2 via one side
  let currentIndex = intersectionEdges[0];
  poly1.push(intersections[0]);

  while (currentIndex !== intersectionEdges[1]) {
    currentIndex = (currentIndex + 1) % polygon.length;
    poly1.push(polygon[currentIndex]);
  }

  poly1.push(intersections[1]);

  // Second polygon: from edge2 to edge1 via the other side
  currentIndex = intersectionEdges[1];
  poly2.push(intersections[1]);

  while (currentIndex !== intersectionEdges[0]) {
    currentIndex = (currentIndex + 1) % polygon.length;
    poly2.push(polygon[currentIndex]);
  }

  poly2.push(intersections[0]);

  return [poly1, poly2];
};

/**
 * Calculate polygon area using the Shoelace formula
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 244:2 - Line 278:4), packages/shared/src/utils/geometry/geometryUtils.ts (Line 199:2 - Line 236:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn28" onclick="toggleCodeBlock('cloneGroup28', 'expandBtn28', 'collapseBtn28')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn28" onclick="toggleCodeBlock('cloneGroup28', 'expandBtn28', 'collapseBtn28')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup28"><code class="language-typescript text-sm text-gray-800">;
};

/**
 * Calculate polygon area using the Shoelace formula
 */
export const calculatePolygonArea = (points: Point[]): number =&gt; {
  let area = 0;

  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    area += points[i].x * points[j].y;
    area -= points[j].x * points[i].y;
  }

  return Math.abs(area / 2);
};

/**
 * Calculate polygon perimeter
 */
export const calculatePolygonPerimeter = (points: Point[]): number =&gt; {
  let perimeter = 0;

  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    perimeter += Math.sqrt(Math.pow(points[j].x - points[i].x, 2) + Math.pow(points[j].y - points[i].y, 2));
  }

  return perimeter;
};

/**
 * Simplify a polygon using the Ramer-Douglas-Peucker algorithm
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 274:2 - Line 290:22), packages/shared/src/utils/geometry/geometryUtils.ts (Line 321:2 - Line 340:22)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn29" onclick="toggleCodeBlock('cloneGroup29', 'expandBtn29', 'collapseBtn29')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn29" onclick="toggleCodeBlock('cloneGroup29', 'expandBtn29', 'collapseBtn29')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup29"><code class="language-typescript text-sm text-gray-800">;

/**
 * Simplify a polygon using the Ramer-Douglas-Peucker algorithm
 */
export const simplifyPolygon = (points: Point[], epsilon: number): Point[] =&gt; {
  if (points.length &lt;= 2) return points;

  // Find the point with the maximum distance
  let maxDistance = 0;
  let index = 0;

  const firstPoint = points[0];
  const lastPoint = points[points.length - 1];

  for (let i = 1; i &lt; points.length - 1; i++) {
    const distance = perpendicularDistance</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 290:22 - Line 310:2), packages/shared/src/utils/geometry/geometryUtils.ts (Line 340:22 - Line 360:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn30" onclick="toggleCodeBlock('cloneGroup30', 'expandBtn30', 'collapseBtn30')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn30" onclick="toggleCodeBlock('cloneGroup30', 'expandBtn30', 'collapseBtn30')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup30"><code class="language-typescript text-sm text-gray-800">(points[i], firstPoint, lastPoint);

    if (distance &gt; maxDistance) {
      maxDistance = distance;
      index = i;
    }
  }

  // If max distance is greater than epsilon, recursively simplify
  if (maxDistance &gt; epsilon) {
    // Recursive call
    const firstHalf = simplifyPolygon(points.slice(0, index + 1), epsilon);
    const secondHalf = simplifyPolygon(points.slice(index), epsilon);

    // Concatenate the two parts
    return firstHalf.slice(0, -1).concat(secondHalf);
  } else {
    // Base case - return just the endpoints
    return [firstPoint, lastPoint];
  }
};</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 310:1 - Line 322:2), packages/shared/src/utils/geometry/geometryUtils.ts (Line 254:1 - Line 271:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn31" onclick="toggleCodeBlock('cloneGroup31', 'expandBtn31', 'collapseBtn31')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn31" onclick="toggleCodeBlock('cloneGroup31', 'expandBtn31', 'collapseBtn31')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup31"><code class="language-typescript text-sm text-gray-800">};

/**
 * Check if a bounding box is visible in the viewport
 * Adds a margin to ensure polygons that are partially visible are included
 */
export const isBoxVisible = (box: BoundingBox, viewport: BoundingBox, margin: number = 100): boolean =&gt; {
  // Add margin to viewport
  const viewportWithMargin = {
    minX: viewport.minX - margin,
    minY: viewport.minY - margin,
    maxX: viewport.maxX + margin,
    maxY: viewport.maxY + margin,</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 323:3 - Line 373:2), packages/shared/src/utils/geometry/geometryUtils.ts (Line 271:3 - Line 321:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn32" onclick="toggleCodeBlock('cloneGroup32', 'expandBtn32', 'collapseBtn32')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn32" onclick="toggleCodeBlock('cloneGroup32', 'expandBtn32', 'collapseBtn32')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup32"><code class="language-typescript text-sm text-gray-800">};

  // Check if the boxes overlap
  return !(
    box.maxX &lt; viewportWithMargin.minX ||
    box.minX &gt; viewportWithMargin.maxX ||
    box.maxY &lt; viewportWithMargin.minY ||
    box.minY &gt; viewportWithMargin.maxY
  );
};

/**
 * Memoize bounding box calculations for polygons
 */
export class PolygonBoundingBoxCache {
  private cache: Map&lt;string, BoundingBox&gt; = new Map();

  /**
   * Get the bounding box for a polygon, calculating it if not cached
   */
  getBoundingBox(polygonId: string, points: Point[]): BoundingBox {
    if (!this.cache.has(polygonId)) {
      this.cache.set(polygonId, calculateBoundingBox(points));
    }
    return this.cache.get(polygonId)!;
  }

  /**
   * Invalidate the cache for a specific polygon
   */
  invalidate(polygonId: string): void {
    this.cache.delete(polygonId);
  }

  /**
   * Clear the entire cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get the number of cached bounding boxes
   */
  size(): number {
    return this.cache.size;
  }
}

// Create a singleton instance
export const polygonBoundingBoxCache = new PolygonBoundingBoxCache();</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/lib/segmentation/dynamicSimplification.ts (Line 124:2 - Line 134:9), packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 284:2 - Line 294:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn34" onclick="toggleCodeBlock('cloneGroup34', 'expandBtn34', 'collapseBtn34')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn34" onclick="toggleCodeBlock('cloneGroup34', 'expandBtn34', 'collapseBtn34')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup34"><code class="language-typescript text-sm text-gray-800">= 0;

  const firstPoint = points[0];
  const lastPoint = points[points.length - 1];

  for (let i = 1; i &lt; points.length - 1; i++) {
    const distance = perpendicularDistance(points[i], firstPoint, lastPoint);

    if (distance &gt; maxDistance) {
      maxDistance = distance;
      maxIndex</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/lib/__mocks__/apiClient.ts (Line 46:3 - Line 71:2), packages/frontend/src/lib/__mocks__/apiClient.ts (Line 11:1 - Line 37:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn35" onclick="toggleCodeBlock('cloneGroup35', 'expandBtn35', 'collapseBtn35')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn35" onclick="toggleCodeBlock('cloneGroup35', 'expandBtn35', 'collapseBtn35')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup35"><code class="language-typescript text-sm text-gray-800">mockGet.mockImplementation((url) =&gt; {
    if (url.includes('/queue-status/project-123')) {
      return Promise.resolve({
        data: {
          queueLength: 1,
          runningTasks: ['task-1'],
          queuedTasks: ['task-2'],
          processingImages: [{ id: 'task-1', name: 'Image 1', projectId: 'project-123' }],
        },
      });
    } else if (url.includes('/queue-status')) {
      return Promise.resolve({
        data: {
          queueLength: 2,
          runningTasks: ['task-1', 'task-3'],
          queuedTasks: ['task-2', 'task-4'],
          processingImages: [
            { id: 'task-1', name: 'Image 1', projectId: 'project-123' },
            { id: 'task-3', name: 'Image 3', projectId: 'project-456' },
          ],
        },
      });
    }
    return Promise.reject(new Error('Not found'));
  });
}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/__tests__/visual-regression/setupVisualRegression.ts (Line 69:8 - Line 89:6), packages/frontend/src/__tests__/visual-regression/setupVisualRegression.ts (Line 42:2 - Line 62:46)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn65" onclick="toggleCodeBlock('cloneGroup65', 'expandBtn65', 'collapseBtn65')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn65" onclick="toggleCodeBlock('cloneGroup65', 'expandBtn65', 'collapseBtn65')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup65"><code class="language-typescript text-sm text-gray-800">);

      // Construct snapshot path
      const snapshotPath = path.join(visualConfig.snapshotsDir, `${name}.png`);

      // If snapshot doesn't exist yet, save it
      if (!fs.existsSync(snapshotPath) &amp;&amp; process.env.UPDATE_VISUAL_SNAPSHOTS) {
        fs.writeFileSync(snapshotPath, screenshot);
        console.log(`Created new snapshot: ${name}`);
        return;
      }

      // Compare with existing snapshot
      expect(screenshot).toMatchImageSnapshot({
        customSnapshotIdentifier: name,
        customSnapshotsDir: visualConfig.snapshotsDir,
        ...visualConfig.comparisonOptions,
      });
    };

    await</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/__tests__/fixtures/polygonFixtures.ts (Line 86:2 - Line 106:2), packages/frontend/src/__tests__/visual-regression/segmentation.visual.spec.ts (Line 69:2 - Line 89:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn66" onclick="toggleCodeBlock('cloneGroup66', 'expandBtn66', 'collapseBtn66')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn66" onclick="toggleCodeBlock('cloneGroup66', 'expandBtn66', 'collapseBtn66')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup66"><code class="language-typescript text-sm text-gray-800">[
    {
      points: [
        { x: 100, y: 100 },
        { x: 200, y: 100 },
        { x: 200, y: 200 },
        { x: 100, y: 200 },
      ],
      closed: true,
      color: '#FF0000',
    },
    {
      points: [
        { x: 300, y: 300 },
        { x: 400, y: 300 },
        { x: 350, y: 400 },
      ],
      closed: true,
      color: '#00FF00',
    },
    {</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/polygonWorkerUtils.ts (Line 1:1 - Line 11:2), packages/frontend/src/shared/utils/polygonWorkerUtils.ts (Line 1:1 - Line 11:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn67" onclick="toggleCodeBlock('cloneGroup67', 'expandBtn67', 'collapseBtn67')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn67" onclick="toggleCodeBlock('cloneGroup67', 'expandBtn67', 'collapseBtn67')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup67"><code class="language-typescript text-sm text-gray-800">import { Point } from '@spheroseg/types';

/**
 * Interface for the polygon worker
 */
export interface PolygonWorker {
  isReady: boolean;
  calculatePolygonArea: (points: Point[]) =&gt; Promise&lt;number&gt;;
  calculatePolygonPerimeter: (points: Point[]) =&gt; Promise&lt;number&gt;;
  calculateBoundingBox: (points: Point[]) =&gt; Promise&lt;{ x: number; y: number; width: number; height: number } | null&gt;;
}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/polygonWorkerUtils.ts (Line 32:5 - Line 49:2), packages/frontend/src/shared/utils/polygonWorkerUtils.ts (Line 35:5 - Line 48:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn68" onclick="toggleCodeBlock('cloneGroup68', 'expandBtn68', 'collapseBtn68')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn68" onclick="toggleCodeBlock('cloneGroup68', 'expandBtn68', 'collapseBtn68')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup68"><code class="language-typescript text-sm text-gray-800">return defaultValue;
  }
};

/**
 * Calculate polygon area using WebWorker
 */
export const calculatePolygonAreaAsync = async (
  points: Point[],
  polygonWorker: PolygonWorker
): Promise&lt;number&gt; =&gt; {
  return executePolygonWorkerOperation(
    points,
    polygonWorker,
    (pts) =&gt; polygonWorker.calculatePolygonArea(pts),
    'calculatePolygonAreaAsync',
    0
  )</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/polygonSlicingUtils.ts (Line 137:7 - Line 142:56), packages/shared/src/utils/geometry/slicingUtils.ts (Line 40:5 - Line 45:57)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn69" onclick="toggleCodeBlock('cloneGroup69', 'expandBtn69', 'collapseBtn69')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn69" onclick="toggleCodeBlock('cloneGroup69', 'expandBtn69', 'collapseBtn69')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup69"><code class="language-typescript text-sm text-gray-800">const distA = Math.pow(a.x - sliceStart.x, 2) + Math.pow(a.y - sliceStart.y, 2);
      const distB = Math.pow(b.x - sliceStart.x, 2) + Math.pow(b.y - sliceStart.y, 2);
      return distA - distB;
    });

    // Use the first and last intersections for a clean cut</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/polygonOperationsUtils.ts (Line 28:8 - Line 154:4), packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 28:4 - Line 138:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn70" onclick="toggleCodeBlock('cloneGroup70', 'expandBtn70', 'collapseBtn70')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn70" onclick="toggleCodeBlock('cloneGroup70', 'expandBtn70', 'collapseBtn70')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup70"><code class="language-typescript text-sm text-gray-800">;
  error?: string;
}

/**
 * Calculate the bounding box of a polygon
 */
export const calculateBoundingBox = (points: Point[]): BoundingBox =&gt; {
  if (!points.length) {
    return { minX: 0, minY: 0, maxX: 0, maxY: 0 };
  }

  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  for (const point of points) {
    minX = Math.min(minX, point.x);
    minY = Math.min(minY, point.y);
    maxX = Math.max(maxX, point.x);
    maxY = Math.max(maxY, point.y);
  }

  return { minX, minY, maxX, maxY };
};

/**
 * Check if a point is inside a polygon using ray casting algorithm
 */
export const isPointInPolygon = (point: Point, polygon: Point[]): boolean =&gt; {
  if (polygon.length &lt; 3) return false;

  let inside = false;
  for (let i = 0, j = polygon.length - 1; i &lt; polygon.length; j = i++) {
    const xi = polygon[i].x;
    const yi = polygon[i].y;
    const xj = polygon[j].x;
    const yj = polygon[j].y;

    const intersect = ((yi &gt; point.y) !== (yj &gt; point.y)) &amp;&amp;
      (point.x &lt; (xj - xi) * (point.y - yi) / (yj - yi) + xi);

    if (intersect) inside = !inside;
  }

  return inside;
};

/**
 * Calculate the intersection point of two line segments
 */
export const calculateIntersection = (
  p1: Point, p2: Point, p3: Point, p4: Point
): Point | null =&gt; {
  // Line 1 represented as a1x + b1y = c1
  const a1 = p2.y - p1.y;
  const b1 = p1.x - p2.x;
  const c1 = a1 * p1.x + b1 * p1.y;

  // Line 2 represented as a2x + b2y = c2
  const a2 = p4.y - p3.y;
  const b2 = p3.x - p4.x;
  const c2 = a2 * p3.x + b2 * p3.y;

  const determinant = a1 * b2 - a2 * b1;

  if (determinant === 0) {
    // Lines are parallel
    return null;
  }

  const x = (b2 * c1 - b1 * c2) / determinant;
  const y = (a1 * c2 - a2 * c1) / determinant;

  // Check if the intersection point is on both line segments
  const onSegment1 = 
    Math.min(p1.x, p2.x) &lt;= x &amp;&amp; x &lt;= Math.max(p1.x, p2.x) &amp;&amp;
    Math.min(p1.y, p2.y) &lt;= y &amp;&amp; y &lt;= Math.max(p1.y, p2.y);
  
  const onSegment2 = 
    Math.min(p3.x, p4.x) &lt;= x &amp;&amp; x &lt;= Math.max(p3.x, p4.x) &amp;&amp;
    Math.min(p3.y, p4.y) &lt;= y &amp;&amp; y &lt;= Math.max(p3.y, p4.y);

  if (onSegment1 &amp;&amp; onSegment2) {
    return { x, y };
  }

  return null;
};

/**
 * Calculate all intersection points between a line and a polygon
 */
export const calculateLinePolygonIntersections = (
  lineStart: Point, 
  lineEnd: Point, 
  polygon: Point[]
): Point[] =&gt; {
  const intersections: Point[] = [];

  for (let i = 0; i &lt; polygon.length; i++) {
    const j = (i + 1) % polygon.length;
    const intersection = calculateIntersection(
      lineStart, lineEnd, polygon[i], polygon[j]
    );

    if (intersection) {
      // Add a small epsilon to avoid duplicate points
      const epsilon = 0.0001;
      const isDuplicate = intersections.some(p =&gt; 
        Math.abs(p.x - intersection.x) &lt; epsilon &amp;&amp; 
        Math.abs(p.y - intersection.y) &lt; epsilon
      );

      if (!isDuplicate) {
        intersections.push(intersection);
      }
    }
  }

  return intersections;
};

/**
 * Calculate perpendicular distance from a point to a line
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/polygonOperationsUtils.ts (Line 141:7 - Line 419:2), packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 129:7 - Line 321:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn71" onclick="toggleCodeBlock('cloneGroup71', 'expandBtn71', 'collapseBtn71')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn71" onclick="toggleCodeBlock('cloneGroup71', 'expandBtn71', 'collapseBtn71')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup71"><code class="language-typescript text-sm text-gray-800">);

      if (!isDuplicate) {
        intersections.push(intersection);
      }
    }
  }

  return intersections;
};

/**
 * Calculate perpendicular distance from a point to a line
 */
export const perpendicularDistance = (
  point: Point, 
  lineStart: Point, 
  lineEnd: Point
): number =&gt; {
  const dx = lineEnd.x - lineStart.x;
  const dy = lineEnd.y - lineStart.y;
  
  // Line length
  const lineLengthSquared = dx * dx + dy * dy;
  
  if (lineLengthSquared === 0) {
    // Line is actually a point
    return Math.sqrt(
      Math.pow(point.x - lineStart.x, 2) + 
      Math.pow(point.y - lineStart.y, 2)
    );
  }
  
  // Calculate the projection of the point onto the line
  const t = ((point.x - lineStart.x) * dx + (point.y - lineStart.y) * dy) / lineLengthSquared;
  
  if (t &lt; 0) {
    // Point is beyond the lineStart end of the line segment
    return Math.sqrt(
      Math.pow(point.x - lineStart.x, 2) + 
      Math.pow(point.y - lineStart.y, 2)
    );
  }
  
  if (t &gt; 1) {
    // Point is beyond the lineEnd end of the line segment
    return Math.sqrt(
      Math.pow(point.x - lineEnd.x, 2) + 
      Math.pow(point.y - lineEnd.y, 2)
    );
  }
  
  // Projection falls on the line segment
  const projectionX = lineStart.x + t * dx;
  const projectionY = lineStart.y + t * dy;
  
  return Math.sqrt(
    Math.pow(point.x - projectionX, 2) + 
    Math.pow(point.y - projectionY, 2)
  );
};

/**
 * Slice a polygon with a line
 */
export const slicePolygon = (
  polygon: Point[], 
  sliceStart: Point, 
  sliceEnd: Point
): Point[][] =&gt; {
  // Find intersections
  const intersections = calculateLinePolygonIntersections(
    sliceStart, sliceEnd, polygon
  );

  // If we don't have exactly 2 intersections, we can't slice properly
  if (intersections.length !== 2) {
    return [polygon]; // Return original polygon
  }

  // Sort intersections by distance from slice start
  intersections.sort((a, b) =&gt; {
    const distA = Math.pow(a.x - sliceStart.x, 2) + Math.pow(a.y - sliceStart.y, 2);
    const distB = Math.pow(b.x - sliceStart.x, 2) + Math.pow(b.y - sliceStart.y, 2);
    return distA - distB;
  });

  // Find the polygon edges that contain the intersections
  const intersectionEdges: number[] = [];
  
  for (const intersection of intersections) {
    for (let i = 0; i &lt; polygon.length; i++) {
      const j = (i + 1) % polygon.length;
      const p1 = polygon[i];
      const p2 = polygon[j];
      
      const intersection2 = calculateIntersection(
        sliceStart, sliceEnd, p1, p2
      );
      
      if (intersection2) {
        const epsilon = 0.0001;
        if (
          Math.abs(intersection.x - intersection2.x) &lt; epsilon &amp;&amp; 
          Math.abs(intersection.y - intersection2.y) &lt; epsilon
        ) {
          intersectionEdges.push(i);
          break;
        }
      }
    }
  }

  // Create two new polygons
  const poly1: Point[] = [];
  const poly2: Point[] = [];

  // First polygon: from edge1 to edge2 via one side
  let currentIndex = intersectionEdges[0];
  poly1.push(intersections[0]);
  
  while (currentIndex !== intersectionEdges[1]) {
    currentIndex = (currentIndex + 1) % polygon.length;
    poly1.push(polygon[currentIndex]);
  }
  
  poly1.push(intersections[1]);

  // Second polygon: from edge2 to edge1 via the other side
  currentIndex = intersectionEdges[1];
  poly2.push(intersections[1]);
  
  while (currentIndex !== intersectionEdges[0]) {
    currentIndex = (currentIndex + 1) % polygon.length;
    poly2.push(polygon[currentIndex]);
  }
  
  poly2.push(intersections[0]);

  return [poly1, poly2];
};

/**
 * Calculate polygon area using the Shoelace formula
 */
export const calculatePolygonArea = (points: Point[]): number =&gt; {
  let area = 0;
  
  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    area += points[i].x * points[j].y;
    area -= points[j].x * points[i].y;
  }
  
  return Math.abs(area / 2);
};

/**
 * Calculate polygon perimeter
 */
export const calculatePolygonPerimeter = (points: Point[]): number =&gt; {
  let perimeter = 0;
  
  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    perimeter += Math.sqrt(
      Math.pow(points[j].x - points[i].x, 2) + 
      Math.pow(points[j].y - points[i].y, 2)
    );
  }
  
  return perimeter;
};

/**
 * Simplify a polygon using the Ramer-Douglas-Peucker algorithm
 */
export const simplifyPolygon = (
  points: Point[], 
  epsilon: number
): Point[] =&gt; {
  if (points.length &lt;= 2) return points;

  // Find the point with the maximum distance
  let maxDistance = 0;
  let index = 0;
  
  const firstPoint = points[0];
  const lastPoint = points[points.length - 1];
  
  for (let i = 1; i &lt; points.length - 1; i++) {
    const distance = perpendicularDistance(points[i], firstPoint, lastPoint);
    
    if (distance &gt; maxDistance) {
      maxDistance = distance;
      index = i;
    }
  }
  
  // If max distance is greater than epsilon, recursively simplify
  if (maxDistance &gt; epsilon) {
    // Recursive call
    const firstHalf = simplifyPolygon(points.slice(0, index + 1), epsilon);
    const secondHalf = simplifyPolygon(points.slice(index), epsilon);
    
    // Concatenate the two parts
    return firstHalf.slice(0, -1).concat(secondHalf);
  } else {
    // Base case - return just the endpoints
    return [firstPoint, lastPoint];
  }
};

/**
 * Check if a bounding box is visible in the viewport
 * Adds a margin to ensure polygons that are partially visible are included
 */
export const isBoxVisible = (
  box: BoundingBox,
  viewport: BoundingBox,
  margin: number = 100
): boolean =&gt; {
  // Add margin to viewport
  const viewportWithMargin = {
    minX: viewport.minX - margin,
    minY: viewport.minY - margin,
    maxX: viewport.maxX + margin,
    maxY: viewport.maxY + margin
  };

  // Check if the boxes overlap
  return !(
    box.maxX &lt; viewportWithMargin.minX ||
    box.minX &gt; viewportWithMargin.maxX ||
    box.maxY &lt; viewportWithMargin.minY ||
    box.minY &gt; viewportWithMargin.maxY
  );
};

/**
 * Memoize bounding box calculations for polygons
 */
export class PolygonBoundingBoxCache {
  private cache: Map&lt;string, BoundingBox&gt; = new Map();
  
  /**
   * Get the bounding box for a polygon, calculating it if not cached
   */
  getBoundingBox(polygonId: string, points: Point[]): BoundingBox {
    if (!this.cache.has(polygonId)) {
      this.cache.set(polygonId, calculateBoundingBox(points));
    }
    return this.cache.get(polygonId)!;
  }
  
  /**
   * Invalidate the cache for a specific polygon
   */
  invalidate(polygonId: string): void {
    this.cache.delete(polygonId);
  }
  
  /**
   * Clear the entire cache
   */
  clear(): void {
    this.cache.clear();
  }
  
  /**
   * Get the number of cached bounding boxes
   */
  size(): number {
    return this.cache.size;
  }
}

// Create a singleton instance
export const polygonBoundingBoxCache = new PolygonBoundingBoxCache();</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/polygonUtils.ts (Line 80:2 - Line 99:4), packages/frontend/src/pages/segmentation/hooks/segmentation/geometry.worker.ts (Line 45:2 - Line 61:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn72" onclick="toggleCodeBlock('cloneGroup72', 'expandBtn72', 'collapseBtn72')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn72" onclick="toggleCodeBlock('cloneGroup72', 'expandBtn72', 'collapseBtn72')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup72"><code class="language-typescript text-sm text-gray-800">= (x: number, y: number, points: Point[]): boolean =&gt; {
  let inside = false;
  for (let i = 0, j = points.length - 1; i &lt; points.length; j = i++) {
    const xi = points[i].x;
    const yi = points[i].y;
    const xj = points[j].x;
    const yj = points[j].y;

    const intersect = yi &gt; y !== yj &gt; y &amp;&amp; x &lt; ((xj - xi) * (y - yi)) / (yj - yi) + xi;
    if (intersect) inside = !inside;
  }
  return inside;
};

/**
 * Check if a point is inside a polygon (alternative version taking a Point object)
 * @param point Point to check
 * @param polygon Array of points defining the polygon
 * @returns True if the point is inside the polygon, false otherwise
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/polygonUtils.ts (Line 194:13 - Line 201:66), packages/shared/src/utils/geometry/geometryUtils.ts (Line 111:6 - Line 120:49)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn73" onclick="toggleCodeBlock('cloneGroup73', 'expandBtn73', 'collapseBtn73')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn73" onclick="toggleCodeBlock('cloneGroup73', 'expandBtn73', 'collapseBtn73')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup73"><code class="language-typescript text-sm text-gray-800">[] = [];

  for (let i = 0; i &lt; polygon.length; i++) {
    const j = (i + 1) % polygon.length;
    const intersection = calculateIntersection(lineStart, lineEnd, polygon[i], polygon[j]);

    if (intersection) {
      // Calculate the distance from the line start to the intersection</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/polygonUtils.ts (Line 204:7 - Line 211:2), packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 125:7 - Line 132:13)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn74" onclick="toggleCodeBlock('cloneGroup74', 'expandBtn74', 'collapseBtn74')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn74" onclick="toggleCodeBlock('cloneGroup74', 'expandBtn74', 'collapseBtn74')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup74"><code class="language-typescript text-sm text-gray-800">// Add a small epsilon to avoid duplicate points
      const epsilon = 0.0001;
      const isDuplicate = intersections.some(
        (p) =&gt; Math.abs(p.x - intersection.x) &lt; epsilon &amp;&amp; Math.abs(p.y - intersection.y) &lt; epsilon,
      );

      if (!isDuplicate) {
        intersections.push({</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/polygonUtils.ts (Line 392:1 - Line 405:4), packages/frontend/src/pages/segmentation/utils/geometry.ts (Line 48:1 - Line 59:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn75" onclick="toggleCodeBlock('cloneGroup75', 'expandBtn75', 'collapseBtn75')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn75" onclick="toggleCodeBlock('cloneGroup75', 'expandBtn75', 'collapseBtn75')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup75"><code class="language-typescript text-sm text-gray-800">export const isClockwise = (points: Point[]): boolean =&gt; {
  let sum = 0;
  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    sum += (points[j].x - points[i].x) * (points[j].y + points[i].y);
  }
  return sum &gt; 0;
};

/**
 * Ensure a polygon is oriented clockwise
 * @param points Array of points defining the polygon
 * @returns Array of points with clockwise orientation
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/polygonUtils.ts (Line 431:1 - Line 450:73), packages/shared/src/utils/geometry/geometryUtils.ts (Line 326:1 - Line 298:65)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn76" onclick="toggleCodeBlock('cloneGroup76', 'expandBtn76', 'collapseBtn76')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn76" onclick="toggleCodeBlock('cloneGroup76', 'expandBtn76', 'collapseBtn76')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup76"><code class="language-typescript text-sm text-gray-800">export const simplifyPolygon = (points: Point[], epsilon: number): Point[] =&gt; {
  if (points.length &lt;= 2) return points;

  // Find the point with the maximum distance
  let maxDistance = 0;
  let index = 0;

  const firstPoint = points[0];
  const lastPoint = points[points.length - 1];

  for (let i = 1; i &lt; points.length - 1; i++) {
    const distance = perpendicularDistance(points[i], firstPoint, lastPoint);

    if (distance &gt; maxDistance) {
      maxDistance = distance;
      index = i;
    }
  }

  // If the maximum distance is greater than epsilon, recursively simplify</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/polygonUtils.ts (Line 538:3 - Line 566:2), packages/shared/src/utils/polygonSlicingUtils.ts (Line 147:5 - Line 175:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn77" onclick="toggleCodeBlock('cloneGroup77', 'expandBtn77', 'collapseBtn77')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn77" onclick="toggleCodeBlock('cloneGroup77', 'expandBtn77', 'collapseBtn77')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup77"><code class="language-typescript text-sm text-gray-800">const polygon1Points: Point[] = [];
  const polygon2Points: Point[] = [];

  // First polygon
  polygon1Points.push({ x: int1.x, y: int1.y });

  let i = (int1.edgeIndex + 1) % polygonPoints.length;
  while (i !== (int2.edgeIndex + 1) % polygonPoints.length) {
    polygon1Points.push({ ...polygonPoints[i] });
    i = (i + 1) % polygonPoints.length;
  }

  polygon1Points.push({ x: int2.x, y: int2.y });

  // Second polygon
  polygon2Points.push({ x: int2.x, y: int2.y });

  i = (int2.edgeIndex + 1) % polygonPoints.length;
  while (i !== (int1.edgeIndex + 1) % polygonPoints.length) {
    polygon2Points.push({ ...polygonPoints[i] });
    i = (i + 1) % polygonPoints.length;
  }

  polygon2Points.push({ x: int1.x, y: int1.y });

  // Ensure both polygons have at least 3 points
  if (polygon1Points.length &gt;= 3 &amp;&amp; polygon2Points.length &gt;= 3) {
    // Create new polygon objects
    const newPolygon1 =</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/logger.ts (Line 114:5 - Line 126:5), packages/frontend/src/utils/logger.ts (Line 97:5 - Line 109:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn78" onclick="toggleCodeBlock('cloneGroup78', 'expandBtn78', 'collapseBtn78')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn78" onclick="toggleCodeBlock('cloneGroup78', 'expandBtn78', 'collapseBtn78')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup78"><code class="language-typescript text-sm text-gray-800">(`[${namespace}] ${message}`, ...args);

      // If first arg is an error object, extract stack trace
      const errorData =
        args[0] instanceof Error
          ? {
              message: args[0].message,
              stack: args[0].stack,
              name: args[0].name,
            }
          : args[0];

      const entry = createLogEntry(LogLevel.WARN</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/logger.ts (Line 131:6 - Line 143:6), packages/frontend/src/utils/logger.ts (Line 97:5 - Line 109:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn79" onclick="toggleCodeBlock('cloneGroup79', 'expandBtn79', 'collapseBtn79')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn79" onclick="toggleCodeBlock('cloneGroup79', 'expandBtn79', 'collapseBtn79')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup79"><code class="language-typescript text-sm text-gray-800">(`[${namespace}] ${message}`, ...args);

      // If first arg is an error object, extract stack trace
      const errorData =
        args[0] instanceof Error
          ? {
              message: args[0].message,
              stack: args[0].stack,
              name: args[0].name,
            }
          : args[0];

      const entry = createLogEntry(LogLevel.ERROR</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/imageUtils.ts (Line 34:12 - Line 52:7), packages/frontend/src/pages/segmentation/utils/imageLoader.ts (Line 28:2 - Line 50:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn80" onclick="toggleCodeBlock('cloneGroup80', 'expandBtn80', 'collapseBtn80')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn80" onclick="toggleCodeBlock('cloneGroup80', 'expandBtn80', 'collapseBtn80')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup80"><code class="language-typescript text-sm text-gray-800">;

    img.onload = () =&gt; {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    };

    img.onerror = () =&gt; {
      console.error(`Failed to load image from ${url}`);
      resolve(null);
    };

    img.src = url;
  });
};

export</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/imageLoader.ts (Line 60:2 - Line 72:7), packages/frontend/src/utils/imageUtils.ts (Line 14:2 - Line 26:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn81" onclick="toggleCodeBlock('cloneGroup81', 'expandBtn81', 'collapseBtn81')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn81" onclick="toggleCodeBlock('cloneGroup81', 'expandBtn81', 'collapseBtn81')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup81"><code class="language-typescript text-sm text-gray-800">= async (url: string): Promise&lt;boolean&gt; =&gt; {
  try {
    const response = await fetch(url, {
      method: 'HEAD',
      cache: 'no-cache',
      headers: {
        'Cache-Control': 'no-cache',
        Pragma: 'no-cache',
      },
    });
    return response.ok;
  } catch (error) {
    logger</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/services/authService.ts (Line 116:2 - Line 123:17), packages/frontend/src/services/authService.ts (Line 28:2 - Line 35:14)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn82" onclick="toggleCodeBlock('cloneGroup82', 'expandBtn82', 'collapseBtn82')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn82" onclick="toggleCodeBlock('cloneGroup82', 'expandBtn82', 'collapseBtn82')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup82"><code class="language-typescript text-sm text-gray-800">= (): string | null =&gt; {
  try {
    if (typeof window === 'undefined' || !document.cookie) return null;

    const cookies = document.cookie.split(';');
    for (let i = 0; i &lt; cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.startsWith('refresh_token='</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/services/authService.ts (Line 202:5 - Line 207:9), packages/frontend/src/services/authService.ts (Line 83:11 - Line 87:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn83" onclick="toggleCodeBlock('cloneGroup83', 'expandBtn83', 'collapseBtn83')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn83" onclick="toggleCodeBlock('cloneGroup83', 'expandBtn83', 'collapseBtn83')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup83"><code class="language-typescript text-sm text-gray-800">const domain = window.location.hostname === 'localhost' ? 'localhost' : window.location.hostname;
    const sameSiteValue = process.env.NODE_ENV === 'production' ? 'lax' : 'lax';
    const secure = window.location.protocol === 'https:' ? '; secure' : '';

    document.cookie = `auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; domain=${domain}; samesite=${sameSiteValue}${secure}`;
    document</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/services/authService.ts (Line 460:13 - Line 487:2), packages/frontend/src/services/authService.ts (Line 366:13 - Line 394:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn84" onclick="toggleCodeBlock('cloneGroup84', 'expandBtn84', 'collapseBtn84')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn84" onclick="toggleCodeBlock('cloneGroup84', 'expandBtn84', 'collapseBtn84')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup84"><code class="language-typescript text-sm text-gray-800">headers: {
              'Content-Type': 'application/json',
            },
            // Je důležité mít withCredentials: true pro správné předávání cookies
            withCredentials: true,
          });

          // Update tokens in localStorage
          const { accessToken, refreshToken } = response.data;
          setTokens(accessToken, refreshToken);

          // Set up cookie as well
          document.cookie = `auth_token=${accessToken}; path=/; samesite=strict; max-age=3600`;

          logger.info('[authService] Development mode login successful');
          return true;
        } finally {
          clearTimeout(timeoutId);
        }
      } catch (devError) {
        logger.error('[authService] Failed to perform development login:', devError);
        return false;
      }
    }

    return false;
  }
}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/lib/svgUtils.ts (Line 64:3 - Line 73:2), packages/frontend/src/pages/segmentation/utils/geometry.ts (Line 7:3 - Line 16:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn105" onclick="toggleCodeBlock('cloneGroup105', 'expandBtn105', 'collapseBtn105')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn105" onclick="toggleCodeBlock('cloneGroup105', 'expandBtn105', 'collapseBtn105')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup105"><code class="language-typescript text-sm text-gray-800">let inside = false;
  for (let i = 0, j = polygon.length - 1; i &lt; polygon.length; j = i++) {
    const xi = polygon[i].x;
    const yi = polygon[i].y;
    const xj = polygon[j].x;
    const yj = polygon[j].y;

    const intersect = yi &gt; point.y !== yj &gt; point.y &amp;&amp; point.x &lt; ((xj - xi) * (point.y - yi)) / (yj - yi) + xi;

    if (intersect) {</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useSegmentationUpdates.ts (Line 187:9 - Line 221:2), packages/frontend/src/hooks/useSegmentationUpdates.ts (Line 83:7 - Line 118:85)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn106" onclick="toggleCodeBlock('cloneGroup106', 'expandBtn106', 'collapseBtn106')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn106" onclick="toggleCodeBlock('cloneGroup106', 'expandBtn106', 'collapseBtn106')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup106"><code class="language-typescript text-sm text-gray-800">// First try the new endpoint
        fetch('/api/queue-status')
          .then((response) =&gt; {
            if (response.ok) {
              return response.json();
            }
            // If that fails, try the legacy endpoint
            return fetch('/api/segmentation/queue').then((legacyResponse) =&gt; {
              if (legacyResponse.ok) {
                return legacyResponse.json();
              }
              throw new Error('Failed to fetch queue status from both endpoints');
            });
          })
          .then((data) =&gt; {
            // Normalize the data structure regardless of which endpoint was used
            const responseData = data.data || data;

            if (responseData) {
              const queueData: QueueStatusUpdate = {
                pendingTasks: responseData.pendingTasks || responseData.queuedTasks || [],
                runningTasks: responseData.runningTasks || [],
                queueLength: responseData.queueLength || 0,
                activeTasksCount: responseData.activeTasksCount || responseData.runningTasks?.length || 0,
                timestamp: responseData.timestamp || new Date().toISOString(),
                processingImages: responseData.processingImages || [],
              };

              setQueueStatus(queueData);

              // Call the queue update callback if provided
              if (onQueueUpdate) {
                onQueueUpdate(queueData);
              }
            }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useSegmentationUpdates.ts (Line 222:11 - Line 241:2), packages/frontend/src/hooks/useSegmentationUpdates.ts (Line 142:9 - Line 162:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn107" onclick="toggleCodeBlock('cloneGroup107', 'expandBtn107', 'collapseBtn107')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn107" onclick="toggleCodeBlock('cloneGroup107', 'expandBtn107', 'collapseBtn107')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup107"><code class="language-typescript text-sm text-gray-800">})
          .catch((error) =&gt; {
            console.error('Error fetching segmentation queue status:', error);

            // Create empty queue status on error
            const emptyQueueData: QueueStatusUpdate = {
              pendingTasks: [],
              runningTasks: [],
              queueLength: 0,
              activeTasksCount: 0,
              timestamp: new Date().toISOString(),
              processingImages: [],
            };

            setQueueStatus(emptyQueueData);

            if (onQueueUpdate) {
              onQueueUpdate(emptyQueueData);
            }
          }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useRecentActivity.ts (Line 255:35 - Line 273:53), packages/frontend/src/hooks/useUserStatistics.ts (Line 295:33 - Line 313:53)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn108" onclick="toggleCodeBlock('cloneGroup108', 'expandBtn108', 'collapseBtn108')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn108" onclick="toggleCodeBlock('cloneGroup108', 'expandBtn108', 'collapseBtn108')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup108"><code class="language-typescript text-sm text-gray-800">;

      if (axios.isAxiosError(err) &amp;&amp; err.response) {
        errorMessage = err.response.data?.message || errorMessage;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }

      setError(errorMessage);

      if (showToasts) {
        toast.error(errorMessage);
      }

      // Try to use cached data even if it's expired
      if (useCache) {
        const { data: cachedData } = loadFromCache();
        if (cachedData) {
          logger.info('Using expired cached activities due to fetch error'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useProjectDuplicate.ts (Line 256:30 - Line 270:7), packages/frontend/src/hooks/useUserStatistics.ts (Line 295:33 - Line 309:47)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn109" onclick="toggleCodeBlock('cloneGroup109', 'expandBtn109', 'collapseBtn109')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn109" onclick="toggleCodeBlock('cloneGroup109', 'expandBtn109', 'collapseBtn109')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup109"><code class="language-typescript text-sm text-gray-800">;

        if (axios.isAxiosError(err) &amp;&amp; err.response) {
          errorMessage = err.response.data?.message || errorMessage;
        } else if (err instanceof Error) {
          errorMessage = err.message;
        }

        setError(errorMessage);

        if (showToasts) {
          toast.error(errorMessage);
        }

        return</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useImageResegment.ts (Line 161:35 - Line 175:35), packages/frontend/src/hooks/useUserStatistics.ts (Line 295:33 - Line 309:47)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn111" onclick="toggleCodeBlock('cloneGroup111', 'expandBtn111', 'collapseBtn111')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn111" onclick="toggleCodeBlock('cloneGroup111', 'expandBtn111', 'collapseBtn111')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup111"><code class="language-typescript text-sm text-gray-800">;

        if (axios.isAxiosError(err) &amp;&amp; err.response) {
          errorMessage = err.response.data?.message || errorMessage;
        } else if (err instanceof Error) {
          errorMessage = err.message;
        }

        setError(errorMessage);

        if (showToasts) {
          toast.error(errorMessage);
        }

        // Revert status if it was changed</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useImageResegment.ts (Line 308:41 - Line 322:40), packages/frontend/src/hooks/useUserStatistics.ts (Line 295:33 - Line 309:47)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn112" onclick="toggleCodeBlock('cloneGroup112', 'expandBtn112', 'collapseBtn112')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn112" onclick="toggleCodeBlock('cloneGroup112', 'expandBtn112', 'collapseBtn112')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup112"><code class="language-typescript text-sm text-gray-800">;

        if (axios.isAxiosError(err) &amp;&amp; err.response) {
          errorMessage = err.response.data?.message || errorMessage;
        } else if (err instanceof Error) {
          errorMessage = err.message;
        }

        setError(errorMessage);

        if (showToasts) {
          toast.error(errorMessage);
        }

        // Revert statuses if they were changed</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useImageDelete.ts (Line 175:25 - Line 189:6), packages/frontend/src/hooks/useUserStatistics.ts (Line 295:33 - Line 309:47)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn113" onclick="toggleCodeBlock('cloneGroup113', 'expandBtn113', 'collapseBtn113')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn113" onclick="toggleCodeBlock('cloneGroup113', 'expandBtn113', 'collapseBtn113')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup113"><code class="language-typescript text-sm text-gray-800">;

        if (axios.isAxiosError(err) &amp;&amp; err.response) {
          errorMessage = err.response.data?.message || errorMessage;
        } else if (err instanceof Error) {
          errorMessage = err.message;
        }

        setError(errorMessage);

        if (showToasts) {
          toast.error(errorMessage);
        }

        throw</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useImageDelete.ts (Line 209:18 - Line 220:27), packages/frontend/src/hooks/useImageResegment.ts (Line 200:22 - Line 211:48)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn114" onclick="toggleCodeBlock('cloneGroup114', 'expandBtn114', 'collapseBtn114')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn114" onclick="toggleCodeBlock('cloneGroup114', 'expandBtn114', 'collapseBtn114')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup114"><code class="language-typescript text-sm text-gray-800">((prev) =&gt; {
        const newState = { ...prev };
        imageIds.forEach((id) =&gt; {
          newState[id] = true;
        });
        return newState;
      });

      setError(null);

      try {
        logger.info('Deleting multiple images'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useImageDelete.ts (Line 263:31 - Line 276:2), packages/frontend/src/hooks/useUserStatistics.ts (Line 295:33 - Line 309:47)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn115" onclick="toggleCodeBlock('cloneGroup115', 'expandBtn115', 'collapseBtn115')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn115" onclick="toggleCodeBlock('cloneGroup115', 'expandBtn115', 'collapseBtn115')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup115"><code class="language-typescript text-sm text-gray-800">;

        if (axios.isAxiosError(err) &amp;&amp; err.response) {
          errorMessage = err.response.data?.message || errorMessage;
        } else if (err instanceof Error) {
          errorMessage = err.message;
        }

        setError(errorMessage);

        if (showToasts) {
          toast.error(errorMessage);
        }
      }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/test-utils/mockDatabase.ts (Line 290:17 - Line 310:20), packages/backend/src/test-utils/mockDatabase.ts (Line 218:46 - Line 238:20)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn127" onclick="toggleCodeBlock('cloneGroup127', 'expandBtn127', 'collapseBtn127')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn127" onclick="toggleCodeBlock('cloneGroup127', 'expandBtn127', 'collapseBtn127')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup127"><code class="language-typescript text-sm text-gray-800">);
      if (whereMatch) {
        const conditions = whereMatch[1];

        // Parse basic key-value conditions
        const conditionParts = conditions.split('AND').map((part) =&gt; part.trim());
        conditionParts.forEach((condition) =&gt; {
          const match = condition.match(/([^\s=&lt;&gt;]+)\s*(=|&lt;&gt;|&gt;|&lt;|&gt;=|&lt;=)\s*(?:'([^']*)'|(\d+)|(\$\d+))/);
          if (match) {
            const [, column, operator, stringValue, numberValue, placeholder] = match;

            // For placeholder values, we'll handle them separately
            if (!placeholder) {
              result.conditions[column] = stringValue || numberValue;
            }
          }
        });
      }
    }

    // Check for DELETE</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/test-utils/mockDatabase.ts (Line 317:2 - Line 341:7), packages/backend/src/test-utils/mockDatabase.ts (Line 286:2 - Line 238:20)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn128" onclick="toggleCodeBlock('cloneGroup128', 'expandBtn128', 'collapseBtn128')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn128" onclick="toggleCodeBlock('cloneGroup128', 'expandBtn128', 'collapseBtn128')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup128"><code class="language-typescript text-sm text-gray-800">;
      }

      // Extract WHERE conditions
      const whereMatch = queryText.match(/WHERE\s+(.+)$/i);
      if (whereMatch) {
        const conditions = whereMatch[1];

        // Parse basic key-value conditions
        const conditionParts = conditions.split('AND').map((part) =&gt; part.trim());
        conditionParts.forEach((condition) =&gt; {
          const match = condition.match(/([^\s=&lt;&gt;]+)\s*(=|&lt;&gt;|&gt;|&lt;|&gt;=|&lt;=)\s*(?:'([^']*)'|(\d+)|(\$\d+))/);
          if (match) {
            const [, column, operator, stringValue, numberValue, placeholder] = match;

            // For placeholder values, we'll handle them separately
            if (!placeholder) {
              result.conditions[column] = stringValue || numberValue;
            }
          }
        });
      }
    }

    return</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/test-utils/mockDatabase.ts (Line 428:11 - Line 442:2), packages/backend/src/test-utils/mockDatabase.ts (Line 379:11 - Line 393:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn129" onclick="toggleCodeBlock('cloneGroup129', 'expandBtn129', 'collapseBtn129')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn129" onclick="toggleCodeBlock('cloneGroup129', 'expandBtn129', 'collapseBtn129')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup129"><code class="language-typescript text-sm text-gray-800">const conditionParts = conditions.split('AND').map((part) =&gt; part.trim());
          conditionParts.forEach((condition, index) =&gt; {
            const match = condition.match(/([^\s=&lt;&gt;]+)\s*(=|&lt;&gt;|&gt;|&lt;|&gt;=|&lt;=)\s*(\$\d+)/);
            if (match) {
              const [, column, operator, placeholder] = match;
              const paramIndex = parseInt(placeholder.substring(1)) - 1;

              if (paramIndex &gt;= 0 &amp;&amp; paramIndex &lt; values.length) {
                parsedQuery.conditions[column] = values[paramIndex];
              }
            }
          });
        }
      }
    }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/test-utils/mockDatabase.ts (Line 498:2 - Line 504:15), packages/backend/src/test-utils/mockDatabase.ts (Line 451:2 - Line 457:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn130" onclick="toggleCodeBlock('cloneGroup130', 'expandBtn130', 'collapseBtn130')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn130" onclick="toggleCodeBlock('cloneGroup130', 'expandBtn130', 'collapseBtn130')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup130"><code class="language-typescript text-sm text-gray-800">= this.tables[parsedQuery.tableName].filter((row) =&gt; {
          return Object.entries(parsedQuery.conditions).every(([column, value]) =&gt; {
            return row[column] === value;
          });
        });

        // Update rows</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/test-utils/mockDatabase.ts (Line 641:2 - Line 651:8), packages/frontend/src/test-utils/apiClientMock.ts (Line 108:18 - Line 118:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn131" onclick="toggleCodeBlock('cloneGroup131', 'expandBtn131', 'collapseBtn131')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn131" onclick="toggleCodeBlock('cloneGroup131', 'expandBtn131', 'collapseBtn131')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup131"><code class="language-typescript text-sm text-gray-800">,
            type: 'external',
            points: [
              { x: 100, y: 100 },
              { x: 200, y: 100 },
              { x: 200, y: 200 },
              { x: 100, y: 200 },
            ],
          },
        ],
        version</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/test-utils/mockAuth.ts (Line 237:7 - Line 261:10), packages/backend/src/test-utils/mockAuth.ts (Line 149:7 - Line 173:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn132" onclick="toggleCodeBlock('cloneGroup132', 'expandBtn132', 'collapseBtn132')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn132" onclick="toggleCodeBlock('cloneGroup132', 'expandBtn132', 'collapseBtn132')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup132"><code class="language-typescript text-sm text-gray-800">} else {
        const [, valueStr, unit] = match;
        const value = parseInt(valueStr, 10);

        switch (unit) {
          case 's':
            expiresAt = new Date(now.getTime() + value * 1000);
            break;
          case 'm':
            expiresAt = new Date(now.getTime() + value * 60000);
            break;
          case 'h':
            expiresAt = new Date(now.getTime() + value * 3600000);
            break;
          case 'd':
            expiresAt = new Date(now.getTime() + value * 86400000);
            break;
          case 'w':
            expiresAt = new Date(now.getTime() + value * 604800000);
            break;
          case 'y':
            expiresAt = new Date(now.getTime() + value * 31536000000);
            break;
          default:
            expiresAt = new Date(now.getTime() + 604800000</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/test-utils/mockAuth.ts (Line 345:2 - Line 365:6), packages/backend/src/test-utils/mockAuth.ts (Line 310:2 - Line 330:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn133" onclick="toggleCodeBlock('cloneGroup133', 'expandBtn133', 'collapseBtn133')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn133" onclick="toggleCodeBlock('cloneGroup133', 'expandBtn133', 'collapseBtn133')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup133"><code class="language-typescript text-sm text-gray-800">|| parts[3] !== this.options.jwtSecret) {
      return null;
    }

    try {
      const payload = JSON.parse(parts[1]) as TokenPayload;

      // Check if token is expired
      if (payload.exp &amp;&amp; payload.exp &lt; Math.floor(Date.now() / 1000)) {
        return null;
      }

      return payload;
    } catch (error) {
      return null;
    }
  }

  /**
   * Create a mock authentication middleware
   */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/services/projectDuplicationService.ts (Line 273:1 - Line 311:4), packages/backend/src/workers/projectDuplicationWorker.ts (Line 185:1 - Line 219:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn134" onclick="toggleCodeBlock('cloneGroup134', 'expandBtn134', 'collapseBtn134')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn134" onclick="toggleCodeBlock('cloneGroup134', 'expandBtn134', 'collapseBtn134')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup134"><code class="language-typescript text-sm text-gray-800">function generateNewFilePaths(
  originalStoragePath: string,
  originalThumbnailPath?: string,
  newProjectId?: string,
): { newStoragePath: string; newThumbnailPath?: string } {
  // Generate timestamp and random suffix for uniqueness
  const timestamp = Date.now();
  const randomSuffix = Math.floor(Math.random() * 1000000);

  // Extract filename from original path
  const originalFileName = originalStoragePath.split('/').pop() || '';
  const fileNameParts = originalFileName.split('.');
  const fileExtension = fileNameParts.pop() || 'png';
  const fileBaseName = fileNameParts.join('.');

  // Generate new storage path
  const newStoragePath = `/uploads/${newProjectId}/${fileBaseName}-copy-${timestamp}-${randomSuffix}.${fileExtension}`;

  // Generate new thumbnail path if original exists
  let newThumbnailPath;
  if (originalThumbnailPath) {
    const originalThumbName = originalThumbnailPath.split('/').pop() || '';
    const thumbNameParts = originalThumbName.split('.');
    const thumbExtension = thumbNameParts.pop() || 'png';
    const thumbBaseName = thumbNameParts.join('.');

    newThumbnailPath = `/uploads/${newProjectId}/thumb-${thumbBaseName}-copy-${timestamp}-${randomSuffix}.${thumbExtension}`;
  }

  return { newStoragePath, newThumbnailPath };
}

/**
 * Copy image files from source to target
 *
 * @param sourcePath Source path (relative to baseDir)
 * @param targetPath Target path (relative to baseDir)
 * @param baseDir Base directory
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/services/projectDuplicationService.ts (Line 312:5 - Line 324:3), packages/backend/src/workers/projectDuplicationWorker.ts (Line 140:8 - Line 152:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn135" onclick="toggleCodeBlock('cloneGroup135', 'expandBtn135', 'collapseBtn135')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn135" onclick="toggleCodeBlock('cloneGroup135', 'expandBtn135', 'collapseBtn135')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup135"><code class="language-typescript text-sm text-gray-800">&gt; {
  try {
    // Normalize paths
    const normalizedSourcePath = sourcePath.startsWith('/') ? sourcePath.substring(1) : sourcePath;
    const normalizedTargetPath = targetPath.startsWith('/') ? targetPath.substring(1) : targetPath;

    // Create full paths
    const fullSourcePath = path.join(baseDir, normalizedSourcePath);
    const fullTargetPath = path.join(baseDir, normalizedTargetPath);

    // Create target directory if it doesn't exist
    const targetDir = path.dirname(fullTargetPath);
    if (!fs</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/services/metricsService.ts (Line 22:3 - Line 36:4), packages/frontend/src/pages/segmentation/hooks/polygonInteraction/geometry/utils/intersectionUtils.ts (Line 81:5 - Line 219:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn136" onclick="toggleCodeBlock('cloneGroup136', 'expandBtn136', 'collapseBtn136')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn136" onclick="toggleCodeBlock('cloneGroup136', 'expandBtn136', 'collapseBtn136')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup136"><code class="language-typescript text-sm text-gray-800">let area = 0;
  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    area += points[i].x * points[j].y;
    area -= points[j].x * points[i].y;
  }

  return Math.abs(area / 2);
};

/**
 * Calculate the perimeter of a polygon
 * @param points Array of points defining the polygon
 * @returns Perimeter of the polygon
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/services/metricsService.ts (Line 59:3 - Line 83:4), packages/frontend/src/utils/polygonUtils.ts (Line 296:3 - Line 320:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn137" onclick="toggleCodeBlock('cloneGroup137', 'expandBtn137', 'collapseBtn137')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn137" onclick="toggleCodeBlock('cloneGroup137', 'expandBtn137', 'collapseBtn137')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup137"><code class="language-typescript text-sm text-gray-800">let minX = points[0].x;
  let minY = points[0].y;
  let maxX = points[0].x;
  let maxY = points[0].y;

  for (let i = 1; i &lt; points.length; i++) {
    minX = Math.min(minX, points[i].x);
    minY = Math.min(minY, points[i].y);
    maxX = Math.max(maxX, points[i].x);
    maxY = Math.max(maxY, points[i].y);
  }

  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY,
  };
};

/**
 * Calculate the convex hull of a polygon using Graham scan algorithm
 * @param points Array of points defining the polygon
 * @returns Array of points defining the convex hull
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/services/metricsService.ts (Line 84:1 - Line 105:5), packages/frontend/src/utils/polygonUtils.ts (Line 321:1 - Line 342:9)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn138" onclick="toggleCodeBlock('cloneGroup138', 'expandBtn138', 'collapseBtn138')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn138" onclick="toggleCodeBlock('cloneGroup138', 'expandBtn138', 'collapseBtn138')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup138"><code class="language-typescript text-sm text-gray-800">export const calculateConvexHull = (points: Point[]): Point[] =&gt; {
  if (points.length &lt;= 3) return [...points];

  // Find the point with the lowest y-coordinate (and leftmost if tied)
  let lowestPoint = points[0];
  for (let i = 1; i &lt; points.length; i++) {
    if (points[i].y &lt; lowestPoint.y || (points[i].y === lowestPoint.y &amp;&amp; points[i].x &lt; lowestPoint.x)) {
      lowestPoint = points[i];
    }
  }

  // Sort points by polar angle with respect to the lowest point
  const sortedPoints = [...points].sort((a, b) =&gt; {
    if (a === lowestPoint) return -1;
    if (b === lowestPoint) return 1;

    const angleA = Math.atan2(a.y - lowestPoint.y, a.x - lowestPoint.x);
    const angleB = Math.atan2(b.y - lowestPoint.y, b.x - lowestPoint.x);

    if (angleA === angleB) {
      // If angles are the same, sort by distance from the lowest point
      const distA = Math</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/services/metricsService.ts (Line 114:3 - Line 122:3), packages/frontend/src/utils/polygonUtils.ts (Line 351:3 - Line 359:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn139" onclick="toggleCodeBlock('cloneGroup139', 'expandBtn139', 'collapseBtn139')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn139" onclick="toggleCodeBlock('cloneGroup139', 'expandBtn139', 'collapseBtn139')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup139"><code class="language-typescript text-sm text-gray-800">const uniquePoints: Point[] = [];
  for (let i = 0; i &lt; sortedPoints.length; i++) {
    if (i === 0 || sortedPoints[i].x !== sortedPoints[i - 1].x || sortedPoints[i].y !== sortedPoints[i - 1].y) {
      uniquePoints.push(sortedPoints[i]);
    }
  }

  // Graham scan algorithm
  if (uniquePoints.length &lt;=</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/users.ts (Line 110:5 - Line 125:32), packages/backend/src/routes/users.ts (Line 25:5 - Line 40:26)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn141" onclick="toggleCodeBlock('cloneGroup141', 'expandBtn141', 'collapseBtn141')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn141" onclick="toggleCodeBlock('cloneGroup141', 'expandBtn141', 'collapseBtn141')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup141"><code class="language-typescript text-sm text-gray-800">// Check if users table exists
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'users'
      )
    `);

    if (!tableCheck.rows[0].exists) {
      logger.warn('Users table does not exist, returning error');
      return res.status(500).json({ message: 'Database schema not initialized' });
    }

    // Check if user already exists</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/users.ts (Line 266:17 - Line 274:22), packages/backend/src/routes/users.ts (Line 17:6 - Line 25:31)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn142" onclick="toggleCodeBlock('cloneGroup142', 'expandBtn142', 'collapseBtn142')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn142" onclick="toggleCodeBlock('cloneGroup142', 'expandBtn142', 'collapseBtn142')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup142"><code class="language-typescript text-sm text-gray-800">, devAuthMiddleware, async (req: AuthenticatedRequest, res: Response) =&gt; {
  const userId = req.user?.userId;

  if (!userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  try {
    // Get projects count</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/users.ts (Line 394:7 - Line 403:2), packages/backend/src/routes/users.ts (Line 363:13 - Line 372:12)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn143" onclick="toggleCodeBlock('cloneGroup143', 'expandBtn143', 'collapseBtn143')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn143" onclick="toggleCodeBlock('cloneGroup143', 'expandBtn143', 'collapseBtn143')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup143"><code class="language-typescript text-sm text-gray-800">: recentActivity
          .filter((item) =&gt; item.type === 'image_uploaded')
          .map((item) =&gt; ({
            id: item.item_id,
            name: item.item_name,
            created_at: item.timestamp,
            project_id: item.project_id,
            project_name: item.project_name,
          })),
      }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/users.ts (Line 421:9 - Line 429:2), packages/backend/src/routes/users.ts (Line 348:15 - Line 356:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn144" onclick="toggleCodeBlock('cloneGroup144', 'expandBtn144', 'collapseBtn144')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn144" onclick="toggleCodeBlock('cloneGroup144', 'expandBtn144', 'collapseBtn144')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup144"><code class="language-typescript text-sm text-gray-800">: recentActivity.map((item) =&gt; ({
        type: item.type,
        item_id: item.item_id,
        item_name: item.item_name,
        timestamp: item.timestamp,
        project_id: item.project_id,
        project_name: item.project_name,
      })),
    }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/users.ts (Line 440:12 - Line 466:66), packages/backend/src/routes/users.ts (Line 17:6 - Line 292:20)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn145" onclick="toggleCodeBlock('cloneGroup145', 'expandBtn145', 'collapseBtn145')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn145" onclick="toggleCodeBlock('cloneGroup145', 'expandBtn145', 'collapseBtn145')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup145"><code class="language-typescript text-sm text-gray-800">, devAuthMiddleware, async (req: AuthenticatedRequest, res: Response) =&gt; {
  const userId = req.user?.userId;

  if (!userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  try {
    // Get projects count
    const projectsCountResult = await pool.query('SELECT COUNT(*) FROM projects WHERE user_id = $1', [userId]);
    const projectsCount = parseInt(projectsCountResult.rows[0].count, 10) || 0;

    // Get images count - join with projects to get all images for this user's projects
    const imagesCountResult = await pool.query(
      'SELECT COUNT(*) FROM images i JOIN projects p ON i.project_id = p.id WHERE p.user_id = $1',
      [userId],
    );
    const imagesCount = parseInt(imagesCountResult.rows[0].count, 10) || 0;

    // Get segmentations count - count completed images as segmentations
    const segmentationsCountResult = await pool.query(
      'SELECT COUNT(*) FROM images i JOIN projects p ON i.project_id = p.id WHERE p.user_id = $1 AND i.status = $2',
      [userId, 'completed'],
    );
    const segmentationsCount = parseInt(segmentationsCountResult.rows[0].count, 10) || 0;

    // Return simplified statistics in both formats for compatibility</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/userStats.ts (Line 10:1 - Line 18:7), packages/backend/src/routes/users.ts (Line 440:1 - Line 25:31)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn146" onclick="toggleCodeBlock('cloneGroup146', 'expandBtn146', 'collapseBtn146')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn146" onclick="toggleCodeBlock('cloneGroup146', 'expandBtn146', 'collapseBtn146')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup146"><code class="language-typescript text-sm text-gray-800">router.get('/me/stats', devAuthMiddleware, async (req: AuthenticatedRequest, res: Response) =&gt; {
  const userId = req.user?.userId;

  if (!userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  try {
    logger</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/userStats.ts (Line 73:1 - Line 81:31), packages/backend/src/routes/users.ts (Line 266:1 - Line 18:26)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn147" onclick="toggleCodeBlock('cloneGroup147', 'expandBtn147', 'collapseBtn147')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn147" onclick="toggleCodeBlock('cloneGroup147', 'expandBtn147', 'collapseBtn147')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup147"><code class="language-typescript text-sm text-gray-800">router.get('/me/statistics', devAuthMiddleware, async (req: AuthenticatedRequest, res: Response) =&gt; {
  const userId = req.user?.userId;

  if (!userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  try {
    logger.info('Fetching statistics for user'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/userStats.ts (Line 81:31 - Line 130:33), packages/backend/src/routes/userStats.ts (Line 18:26 - Line 67:28)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn148" onclick="toggleCodeBlock('cloneGroup148', 'expandBtn148', 'collapseBtn148')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn148" onclick="toggleCodeBlock('cloneGroup148', 'expandBtn148', 'collapseBtn148')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup148"><code class="language-typescript text-sm text-gray-800">, { userId });

    // Get stats from service
    const stats = await userStatsService.getUserStats(pool, userId);

    // Return stats with both new format and old format keys for compatibility
    res.status(200).json({
      // New format property names
      totalProjects: stats.totalProjects,
      totalImages: stats.totalImages,
      completedSegmentations: stats.completedSegmentations,
      storageUsedBytes: stats.storageUsedBytes.toString(),
      storageLimitBytes: stats.storageLimitBytes.toString(),
      storageUsedMB: Number(stats.storageUsedBytes) &gt; 0
        ? Math.round((Number(stats.storageUsedBytes) / (1024 * 1024)) * 100) / 100
        : 0.01, // Use a small non-zero value if no storage used
      recentActivity: stats.recentActivity,
      recentProjects: stats.recentProjects,
      recentImages: stats.recentImages,
      comparisons: {
        projectsThisMonth: stats.projectsThisMonth,
        projectsLastMonth: stats.projectsLastMonth,
        projectsChange: stats.projectsThisMonth - stats.projectsLastMonth,
        imagesThisMonth: stats.imagesThisMonth,
        imagesLastMonth: stats.imagesLastMonth,
        imagesChange: stats.imagesThisMonth - stats.imagesLastMonth,
      },

      // Old format property names for compatibility
      projects_count: stats.totalProjects,
      images_count: stats.totalImages,
      segmentations_count: stats.completedSegmentations,
      storage_used_mb: Number(stats.storageUsedBytes) &gt; 0
        ? Math.round((Number(stats.storageUsedBytes) / (1024 * 1024)) * 100) / 100
        : 0.01, // Use a small non-zero value if no storage used
      storage_used_bytes: stats.storageUsedBytes.toString(),
      storage_limit_bytes: stats.storageLimitBytes.toString(),
      last_login: new Date().toISOString(),
      recent_activity: stats.recentActivity,
      recent_projects: stats.recentProjects,
      recent_images: stats.recentImages,
      projects_this_month: stats.projectsThisMonth,
      projects_last_month: stats.projectsLastMonth,
      projects_change: stats.projectsThisMonth - stats.projectsLastMonth,
      images_this_month: stats.imagesThisMonth,
      images_last_month: stats.imagesLastMonth,
      images_change: stats.imagesThisMonth - stats.imagesLastMonth,
    });
  } catch (error) {
    logger.error('Error fetching user statistics'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/status.ts (Line 212:32 - Line 224:54), packages/backend/src/routes/status.ts (Line 83:27 - Line 95:25)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn149" onclick="toggleCodeBlock('cloneGroup149', 'expandBtn149', 'collapseBtn149')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn149" onclick="toggleCodeBlock('cloneGroup149', 'expandBtn149', 'collapseBtn149')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup149"><code class="language-typescript text-sm text-gray-800">,
  authMiddleware,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) =&gt; {
    const userId = req.user?.userId;
    const projectId = req.params.projectId;

    if (!userId) {
      res.status(401).json({ message: 'Authentication error' });
      return;
    }

    try {
      // Return mock data specific to the requested project</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/segmentation.ts (Line 135:14 - Line 160:39), packages/backend/src/routes/segmentation.ts (Line 58:8 - Line 83:38)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn150" onclick="toggleCodeBlock('cloneGroup150', 'expandBtn150', 'collapseBtn150')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn150" onclick="toggleCodeBlock('cloneGroup150', 'expandBtn150', 'collapseBtn150')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup150"><code class="language-typescript text-sm text-gray-800">,
          status: 'pending',
          result_data: {
            polygons: [],
          },
          polygons: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
        res.status(200).json(emptyResult);
        return;
      }

      // Format the result
      const segmentationResult = result.rows[0];

      // Ensure polygons are available in the expected format
      if (segmentationResult.result_data &amp;&amp; segmentationResult.result_data.polygons) {
        segmentationResult.polygons = segmentationResult.result_data.polygons;
      } else if (!segmentationResult.polygons) {
        segmentationResult.polygons = [];
      }

      res.status(200).json(segmentationResult);
    } catch (error) {
      console.error('Error fetching segmentation results:'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/segmentation.ts (Line 467:5 - Line 486:4), packages/backend/src/routes/segmentation.ts (Line 96:7 - Line 115:54)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn151" onclick="toggleCodeBlock('cloneGroup151', 'expandBtn151', 'collapseBtn151')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn151" onclick="toggleCodeBlock('cloneGroup151', 'expandBtn151', 'collapseBtn151')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup151"><code class="language-typescript text-sm text-gray-800">;

    if (!userId) {
      res.status(401).json({ message: 'Authentication error' });
      return;
    }

    try {
      // Verify user has access to the project
      const projectCheck = await pool.query('SELECT id FROM projects WHERE id = $1 AND user_id = $2', [
        projectId,
        userId,
      ]);

      if (projectCheck.rows.length === 0) {
        res.status(404).json({ message: 'Project not found or access denied' });
        return;
      }

      let</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/segmentation.ts (Line 558:5 - Line 589:39), packages/backend/src/routes/segmentation.ts (Line 98:5 - Line 129:29)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn152" onclick="toggleCodeBlock('cloneGroup152', 'expandBtn152', 'collapseBtn152')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn152" onclick="toggleCodeBlock('cloneGroup152', 'expandBtn152', 'collapseBtn152')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup152"><code class="language-typescript text-sm text-gray-800">if (!userId) {
      res.status(401).json({ message: 'Authentication error' });
      return;
    }

    try {
      // Verify user has access to the project
      const projectCheck = await pool.query('SELECT id FROM projects WHERE id = $1 AND user_id = $2', [
        projectId,
        userId,
      ]);

      if (projectCheck.rows.length === 0) {
        res.status(404).json({ message: 'Project not found or access denied' });
        return;
      }

      // Verify image belongs to the project using its UUID
      const imageCheck = await pool.query('SELECT id FROM images WHERE id = $1 AND project_id = $2', [
        imageId,
        projectId,
      ]);

      if (imageCheck.rows.length === 0) {
        res.status(404).json({ message: 'Image not found in this project' });
        return;
      }

      // Use the actual image ID from the database for the segmentation lookup (which is just imageId now)
      const actualImageId = imageId; // Directly use the validated imageId

      // Check if segmentation result exists</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/segmentation.ts (Line 646:18 - Line 655:27), packages/backend/src/routes/status.ts (Line 167:15 - Line 177:61)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn153" onclick="toggleCodeBlock('cloneGroup153', 'expandBtn153', 'collapseBtn153')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn153" onclick="toggleCodeBlock('cloneGroup153', 'expandBtn153', 'collapseBtn153')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup153"><code class="language-typescript text-sm text-gray-800">, async (req: AuthenticatedRequest, res: Response, next: NextFunction) =&gt; {
  const userId = req.user?.userId;

  if (!userId) {
    res.status(401).json({ message: 'Authentication error' });
    return;
  }

  try {
    // Get global queue status</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/segmentation.ts (Line 718:2 - Line 737:37), packages/backend/src/routes/segmentation.ts (Line 96:7 - Line 115:54)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn154" onclick="toggleCodeBlock('cloneGroup154', 'expandBtn154', 'collapseBtn154')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn154" onclick="toggleCodeBlock('cloneGroup154', 'expandBtn154', 'collapseBtn154')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup154"><code class="language-typescript text-sm text-gray-800">;

    if (!userId) {
      res.status(401).json({ message: 'Authentication error' });
      return;
    }

    try {
      // Verify user has access to the project
      const projectCheck = await pool.query('SELECT id FROM projects WHERE id = $1 AND user_id = $2', [
        projectId,
        userId,
      ]);

      if (projectCheck.rows.length === 0) {
        res.status(404).json({ message: 'Project not found or access denied' });
        return;
      }

      // Get queue status for this project</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projects.ts (Line 280:20 - Line 288:36), packages/backend/src/routes/projects.ts (Line 202:16 - Line 210:33)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn155" onclick="toggleCodeBlock('cloneGroup155', 'expandBtn155', 'collapseBtn155')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn155" onclick="toggleCodeBlock('cloneGroup155', 'expandBtn155', 'collapseBtn155')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup155"><code class="language-typescript text-sm text-gray-800">),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) =&gt; {
    const userId = req.user?.userId;
    const { id: projectId } = req.params;

    if (!userId) return res.status(401).json({ message: 'Authentication error' });

    try {
      logger.info('Processing delete project request'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projects.ts (Line 290:7 - Line 312:40), packages/backend/src/routes/projects.ts (Line 228:7 - Line 250:33)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn156" onclick="toggleCodeBlock('cloneGroup156', 'expandBtn156', 'collapseBtn156')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn156" onclick="toggleCodeBlock('cloneGroup156', 'expandBtn156', 'collapseBtn156')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup156"><code class="language-typescript text-sm text-gray-800">// First check if the projects table exists
      const projectsTableCheck = await pool.query(`
            SELECT EXISTS (
                SELECT 1
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'projects'
            )
        `);

      const projectsTableExists = projectsTableCheck.rows[0].exists;
      if (!projectsTableExists) {
        logger.warn('Projects table does not exist in database');
        return res.status(404).json({
          message: 'Project not found - projects table missing',
          error: 'NOT_FOUND',
        });
      }

      // Import and use projectService
      const projectService = await import('../services/projectService');

      // Delete the project using the service</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projects.ts (Line 353:7 - Line 374:60), packages/backend/src/routes/projects.ts (Line 288:2 - Line 247:33)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn157" onclick="toggleCodeBlock('cloneGroup157', 'expandBtn157', 'collapseBtn157')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn157" onclick="toggleCodeBlock('cloneGroup157', 'expandBtn157', 'collapseBtn157')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup157"><code class="language-typescript text-sm text-gray-800">});

      // First check if the projects table exists
      const projectsTableCheck = await pool.query(`
            SELECT EXISTS (
                SELECT 1
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'projects'
            )
        `);

      const projectsTableExists = projectsTableCheck.rows[0].exists;
      if (!projectsTableExists) {
        logger.warn('Projects table does not exist in database');
        return res.status(404).json({
          message: 'Project not found - projects table missing',
          error: 'NOT_FOUND',
        });
      }

      // Verify the source project exists and belongs to the user</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projectDuplicationRoutes.ts (Line 21:2 - Line 31:2), packages/backend/src/validators/projectValidators.ts (Line 9:2 - Line 19:14)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn158" onclick="toggleCodeBlock('cloneGroup158', 'expandBtn158', 'collapseBtn158')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn158" onclick="toggleCodeBlock('cloneGroup158', 'expandBtn158', 'collapseBtn158')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup158"><code class="language-typescript text-sm text-gray-800">= z.object({
  query: z.object({
    limit: z
      .string()
      .optional()
      .transform((val) =&gt; (val ? parseInt(val, 10) : 10)),
    offset: z
      .string()
      .optional()
      .transform((val) =&gt; (val ? parseInt(val, 10) : 0)),
  }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projectDuplicationRoutes.ts (Line 51:15 - Line 56:2), packages/backend/src/routes/projects.ts (Line 338:18 - Line 343:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn159" onclick="toggleCodeBlock('cloneGroup159', 'expandBtn159', 'collapseBtn159')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn159" onclick="toggleCodeBlock('cloneGroup159', 'expandBtn159', 'collapseBtn159')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup159"><code class="language-typescript text-sm text-gray-800">,
  validate(duplicateProjectSchema),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) =&gt; {
    const userId = req.user?.userId;
    const { id: originalProjectId } = req.params;
    const { newTitle, copyFiles = true, copySegmentations = false, resetStatus = true }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projectDuplicationRoutes.ts (Line 75:6 - Line 86:47), packages/backend/src/routes/projects.ts (Line 236:10 - Line 247:33)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn160" onclick="toggleCodeBlock('cloneGroup160', 'expandBtn160', 'collapseBtn160')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn160" onclick="toggleCodeBlock('cloneGroup160', 'expandBtn160', 'collapseBtn160')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup160"><code class="language-typescript text-sm text-gray-800">);

      const projectsTableExists = projectsTableCheck.rows[0].exists;
      if (!projectsTableExists) {
        logger.warn('Projects table does not exist in database');
        return res.status(404).json({
          message: 'Project not found - projects table missing',
          error: 'NOT_FOUND',
        });
      }

      // Check if the duplication tasks table exists</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projectDuplicationRoutes.ts (Line 103:9 - Line 117:39), packages/backend/src/routes/projects.ts (Line 374:7 - Line 388:79)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn161" onclick="toggleCodeBlock('cloneGroup161', 'expandBtn161', 'collapseBtn161')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn161" onclick="toggleCodeBlock('cloneGroup161', 'expandBtn161', 'collapseBtn161')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup161"><code class="language-typescript text-sm text-gray-800">// Verify the source project exists and belongs to the user
        const projectCheck = await pool.query('SELECT * FROM projects WHERE id = $1 AND user_id = $2', [
          originalProjectId,
          userId,
        ]);

        if (projectCheck.rows.length === 0) {
          logger.info('Source project not found or access denied', {
            originalProjectId,
            userId,
          });
          return res.status(404).json({ message: 'Source project not found or access denied' });
        }

        // Duplicate the project synchronously</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projectDuplicationRoutes.ts (Line 132:11 - Line 149:40), packages/backend/src/routes/projects.ts (Line 371:2 - Line 388:79)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn162" onclick="toggleCodeBlock('cloneGroup162', 'expandBtn162', 'collapseBtn162')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn162" onclick="toggleCodeBlock('cloneGroup162', 'expandBtn162', 'collapseBtn162')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup162"><code class="language-typescript text-sm text-gray-800">);
      }

      // Verify the source project exists and belongs to the user
      const projectCheck = await pool.query('SELECT * FROM projects WHERE id = $1 AND user_id = $2', [
        originalProjectId,
        userId,
      ]);

      if (projectCheck.rows.length === 0) {
        logger.info('Source project not found or access denied', {
          originalProjectId,
          userId,
        });
        return res.status(404).json({ message: 'Source project not found or access denied' });
      }

      // Import the duplication queue service</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projectDuplicationRoutes.ts (Line 149:7 - Line 174:4), packages/backend/src/routes/projects.ts (Line 403:13 - Line 428:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn163" onclick="toggleCodeBlock('cloneGroup163', 'expandBtn163', 'collapseBtn163')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn163" onclick="toggleCodeBlock('cloneGroup163', 'expandBtn163', 'collapseBtn163')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup163"><code class="language-typescript text-sm text-gray-800">// Import the duplication queue service
      const projectDuplicationQueueService = await import('../services/projectDuplicationQueueService');

      // Trigger an asynchronous duplication
      const taskId = await projectDuplicationQueueService.default.triggerProjectDuplication(
        pool,
        originalProjectId,
        userId,
        {
          newTitle,
          copyFiles,
          copySegmentations,
          resetStatus,
          baseDir: process.cwd(),
        },
      );

      logger.info('Project duplication task created successfully', {
        originalProjectId,
        taskId,
        userId,
        options: { newTitle, copyFiles, copySegmentations, resetStatus },
      });

      // Return the task ID and status
      res</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projectDuplicationRoutes.ts (Line 322:24 - Line 330:30), packages/backend/src/routes/projectDuplicationRoutes.ts (Line 265:21 - Line 273:28)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn164" onclick="toggleCodeBlock('cloneGroup164', 'expandBtn164', 'collapseBtn164')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn164" onclick="toggleCodeBlock('cloneGroup164', 'expandBtn164', 'collapseBtn164')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup164"><code class="language-typescript text-sm text-gray-800">),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) =&gt; {
    const userId = req.user?.userId;
    const { taskId } = req.params;

    if (!userId) return res.status(401).json({ message: 'Authentication error' });

    try {
      logger.info('Cancelling duplication task'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projectDuplicationRoutes.ts (Line 330:30 - Line 351:19), packages/backend/src/routes/projectDuplicationRoutes.ts (Line 273:28 - Line 294:16)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn165" onclick="toggleCodeBlock('cloneGroup165', 'expandBtn165', 'collapseBtn165')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn165" onclick="toggleCodeBlock('cloneGroup165', 'expandBtn165', 'collapseBtn165')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup165"><code class="language-typescript text-sm text-gray-800">, { userId, taskId });

      // Check if the duplication tasks table exists
      const tasksTableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'project_duplication_tasks'
      )
    `);

      const tasksTableExists = tasksTableCheck.rows[0].exists;
      if (!tasksTableExists) {
        logger.warn('Project duplication tasks table does not exist');
        return res.status(404).json({ message: 'Task not found - tasks table missing' });
      }

      // Import the duplication queue service
      const projectDuplicationQueueService = await import('../services/projectDuplicationQueueService');

      // Cancel the task</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/performance.ts (Line 69:1 - Line 77:45), packages/backend/src/routes/users.ts (Line 17:1 - Line 25:31)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn166" onclick="toggleCodeBlock('cloneGroup166', 'expandBtn166', 'collapseBtn166')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn166" onclick="toggleCodeBlock('cloneGroup166', 'expandBtn166', 'collapseBtn166')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup166"><code class="language-typescript text-sm text-gray-800">router.get('/me', devAuthMiddleware, async (req: AuthenticatedRequest, res: Response) =&gt; {
  const userId = req.user?.userId;

  if (!userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  try {
    // Check if performance_metrics table exists</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/logs.ts (Line 313:7 - Line 339:10), packages/backend/src/routes/logs.ts (Line 226:4 - Line 251:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn167" onclick="toggleCodeBlock('cloneGroup167', 'expandBtn167', 'collapseBtn167')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn167" onclick="toggleCodeBlock('cloneGroup167', 'expandBtn167', 'collapseBtn167')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup167"><code class="language-typescript text-sm text-gray-800">('/admin', authMiddleware, async (req: AuthenticatedRequest, res: Response) =&gt; {
  try {
    const userId = req.user?.userId;

    // Admin role check
    if (!userId) {
      return res.status(401).json({ success: false, message: 'Authentication required' });
    }

    // Check if user has admin role
    try {
      const userResult = await pool.query('SELECT role FROM users WHERE id = $1', [userId]);

      if (userResult.rows.length === 0 || userResult.rows[0].role !== 'admin') {
        return res.status(403).json({ success: false, message: 'Admin access required' });
      }
    } catch (roleError) {
      logger.error('Error checking admin role:', { error: roleError });
      // In development mode, allow access even without admin role check
      if (process.env.NODE_ENV !== 'development') {
        return res.status(500).json({ success: false, message: 'Error checking admin access' });
      }
    }

    // Extract query parameters
    const {
      olderThan</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/logs.ts (Line 363:5 - Line 373:17), packages/backend/src/routes/logs.ts (Line 258:5 - Line 268:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn168" onclick="toggleCodeBlock('cloneGroup168', 'expandBtn168', 'collapseBtn168')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn168" onclick="toggleCodeBlock('cloneGroup168', 'expandBtn168', 'collapseBtn168')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup168"><code class="language-typescript text-sm text-gray-800">if (level !== undefined) {
      query += ` AND level = $${params.length + 1}`;
      params.push(Number(level));
    }

    if (source) {
      query += ` AND source = $${params.length + 1}`;
      params.push(source);
    }

    // Execute query</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/logs.ts (Line 388:9 - Line 397:56), packages/backend/src/routes/logs.ts (Line 226:9 - Line 235:32)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn169" onclick="toggleCodeBlock('cloneGroup169', 'expandBtn169', 'collapseBtn169')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn169" onclick="toggleCodeBlock('cloneGroup169', 'expandBtn169', 'collapseBtn169')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup169"><code class="language-typescript text-sm text-gray-800">, authMiddleware, async (req: AuthenticatedRequest, res: Response) =&gt; {
  try {
    const userId = req.user?.userId;

    // Admin role check
    if (!userId) {
      return res.status(401).json({ success: false, message: 'Authentication required' });
    }

    // Check if user has admin role (except in development)</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/logs.ts (Line 399:7 - Line 407:7), packages/backend/src/routes/logs.ts (Line 236:5 - Line 244:67)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn170" onclick="toggleCodeBlock('cloneGroup170', 'expandBtn170', 'collapseBtn170')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn170" onclick="toggleCodeBlock('cloneGroup170', 'expandBtn170', 'collapseBtn170')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup170"><code class="language-typescript text-sm text-gray-800">try {
        const userResult = await pool.query('SELECT role FROM users WHERE id = $1', [userId]);

        if (userResult.rows.length === 0 || userResult.rows[0].role !== 'admin') {
          return res.status(403).json({ success: false, message: 'Admin access required' });
        }
      } catch (roleError) {
        logger.error('Error checking admin role:', { error: roleError });
        return</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/images.ts (Line 759:10 - Line 778:5), packages/backend/src/routes/images.ts (Line 687:8 - Line 705:57)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn171" onclick="toggleCodeBlock('cloneGroup171', 'expandBtn171', 'collapseBtn171')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn171" onclick="toggleCodeBlock('cloneGroup171', 'expandBtn171', 'collapseBtn171')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup171"><code class="language-typescript text-sm text-gray-800">,
    });

    try {
      const projectCheck = await pool.query('SELECT id FROM projects WHERE id = $1 AND user_id = $2', [
        projectId,
        userId,
      ]);

      if (projectCheck.rows.length === 0) {
        logger.warn('Project access denied', {
          projectId,
          originalProjectId,
          userId,
        });
        throw new ApiError('Project not found or access denied', 404);
      }

      const imageResult = await pool.query(
        name</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/images.ts (Line 836:18 - Line 845:38), packages/backend/src/routes/images.ts (Line 519:2 - Line 528:34)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn172" onclick="toggleCodeBlock('cloneGroup172', 'expandBtn172', 'collapseBtn172')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn172" onclick="toggleCodeBlock('cloneGroup172', 'expandBtn172', 'collapseBtn172')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup172"><code class="language-typescript text-sm text-gray-800">,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) =&gt; {
    const userId = req.user?.userId;
    const { projectId } = req.params;
    const imageId = req.params.imageId;

    // No need to handle project- prefix anymore as it's been removed from the frontend
    const originalProjectId = projectId;

    logger.info('Image verification request received'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/images.ts (Line 845:38 - Line 867:72), packages/backend/src/routes/images.ts (Line 683:32 - Line 705:57)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn173" onclick="toggleCodeBlock('cloneGroup173', 'expandBtn173', 'collapseBtn173')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn173" onclick="toggleCodeBlock('cloneGroup173', 'expandBtn173', 'collapseBtn173')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup173"><code class="language-typescript text-sm text-gray-800">, {
      userId,
      projectId,
      originalProjectId,
      imageId,
    });

    try {
      const projectCheck = await pool.query('SELECT id FROM projects WHERE id = $1 AND user_id = $2', [
        projectId,
        userId,
      ]);

      if (projectCheck.rows.length === 0) {
        logger.warn('Project access denied', {
          projectId,
          originalProjectId,
          userId,
        });
        throw new ApiError('Project not found or access denied', 404);
      }

      const imageResult = await pool.query('SELECT id, storage_path FROM images WHERE id = $1 AND project_id = $2'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/auth.ts (Line 431:6 - Line 439:29), packages/backend/src/routes/auth.ts (Line 410:10 - Line 418:39)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn174" onclick="toggleCodeBlock('cloneGroup174', 'expandBtn174', 'collapseBtn174')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn174" onclick="toggleCodeBlock('cloneGroup174', 'expandBtn174', 'collapseBtn174')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup174"><code class="language-typescript text-sm text-gray-800">, authMiddleware, async (req: AuthenticatedRequest, res: Response) =&gt; {
  const userId = req.user?.userId;

  if (!userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    logger.info('Fetching current user data'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/middleware/rateLimitMiddleware.ts (Line 92:42 - Line 99:7), packages/backend/src/middleware/rateLimitMiddleware.ts (Line 69:27 - Line 76:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn175" onclick="toggleCodeBlock('cloneGroup175', 'expandBtn175', 'collapseBtn175')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn175" onclick="toggleCodeBlock('cloneGroup175', 'expandBtn175', 'collapseBtn175')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup175"><code class="language-typescript text-sm text-gray-800">, {
            ip: req.ip,
            path: req.path,
            method: req.method,
            userId: (req as any).user?.userId,
          });
          res.status(429).json(options.message);
          res.set('Retry-After', '3600'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/middleware/rateLimitMiddleware.ts (Line 108:9 - Line 115:22), packages/backend/src/middleware/rateLimitMiddleware.ts (Line 62:9 - Line 69:27)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn176" onclick="toggleCodeBlock('cloneGroup176', 'expandBtn176', 'collapseBtn176')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn176" onclick="toggleCodeBlock('cloneGroup176', 'expandBtn176', 'collapseBtn176')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup176"><code class="language-typescript text-sm text-gray-800">message: {
          error: {
            message: 'Too many requests, please try again later',
            timestamp: new Date().toISOString(),
          },
        },
        handler: (req: Request, res: Response, _next: NextFunction, options: any) =&gt; {
          logger.warn('Rate limit exceeded'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/middleware/rateLimitMiddleware.ts (Line 115:22 - Line 126:2), packages/backend/src/middleware/rateLimitMiddleware.ts (Line 69:27 - Line 80:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn177" onclick="toggleCodeBlock('cloneGroup177', 'expandBtn177', 'collapseBtn177')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn177" onclick="toggleCodeBlock('cloneGroup177', 'expandBtn177', 'collapseBtn177')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup177"><code class="language-typescript text-sm text-gray-800">, {
            ip: req.ip,
            path: req.path,
            method: req.method,
            userId: (req as any).user?.userId,
          });
          res.status(429).json(options.message);
          res.set('Retry-After', '900'); // 15 minutes in seconds
        },
      };
      break;
  }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/middleware/rateLimitMiddleware.ts (Line 189:5 - Line 196:8), packages/backend/src/middleware/rateLimitMiddleware.ts (Line 91:6 - Line 98:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn178" onclick="toggleCodeBlock('cloneGroup178', 'expandBtn178', 'collapseBtn178')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn178" onclick="toggleCodeBlock('cloneGroup178', 'expandBtn178', 'collapseBtn178')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup178"><code class="language-typescript text-sm text-gray-800">: NextFunction, options: any) =&gt; {
    logger.warn('Sensitive operation rate limit exceeded', {
      ip: req.ip,
      path: req.path,
      method: req.method,
      userId: (req as any).user?.userId,
    });
    res.status(options</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/middleware/authMiddleware.ts (Line 167:5 - Line 190:37), packages/backend/src/middleware/authMiddleware.ts (Line 51:5 - Line 74:22)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn179" onclick="toggleCodeBlock('cloneGroup179', 'expandBtn179', 'collapseBtn179')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn179" onclick="toggleCodeBlock('cloneGroup179', 'expandBtn179', 'collapseBtn179')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup179"><code class="language-typescript text-sm text-gray-800">const decoded = tokenService.verifyToken(token, TokenType.ACCESS, {
      validateFingerprint: config.auth.tokenSecurityMode === 'strict',
      requiredIssuer: 'spheroseg-auth',
      requiredAudience: 'spheroseg-api',
    });

    // Add user data to request
    req.user = {
      userId: decoded.userId,
      email: decoded.email,
      type: decoded.type,
      // Add additional claims for enhanced security
      tokenId: decoded.jti,
      fingerprint: decoded.fingerprint,
      tokenVersion: decoded.version,
    };

    // Add token metadata to request for access in routes
    req.tokenMetadata = {
      issuedAt: new Date((decoded as any).iat * 1000),
      expiresAt: new Date((decoded as any).exp * 1000),
    };

    logger.debug(`Optional auth: User authenticated: </code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/db/optimized.ts (Line 295:2 - Line 305:6), packages/backend/src/db/optimizedQueries.ts (Line 94:2 - Line 103:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn180" onclick="toggleCodeBlock('cloneGroup180', 'expandBtn180', 'collapseBtn180')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn180" onclick="toggleCodeBlock('cloneGroup180', 'expandBtn180', 'collapseBtn180')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup180"><code class="language-typescript text-sm text-gray-800">{
  const client = await pool.connect();

  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/controllers/segmentationController.ts (Line 163:2 - Line 181:7), packages/backend/src/controllers/segmentationController.ts (Line 134:2 - Line 152:12)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn181" onclick="toggleCodeBlock('cloneGroup181', 'expandBtn181', 'collapseBtn181')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn181" onclick="toggleCodeBlock('cloneGroup181', 'expandBtn181', 'collapseBtn181')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup181"><code class="language-typescript text-sm text-gray-800">= async (req: Request, res: Response, next: NextFunction) =&gt; {
  try {
    const imageId = req.params.imageId;
    const versionStr = req.params.version;

    if (!imageId) {
      throw new ApiError(400, 'Image ID is required');
    }

    if (!versionStr) {
      throw new ApiError(400, 'Version is required');
    }

    const version = parseInt(versionStr, 10);
    if (isNaN(version) || version &lt;= 0) {
      throw new ApiError(400, 'Invalid version number');
    }

    const result</code></pre></div><div class="py-4"><p class="text-gray-600">packages/types/src/user.ts (Line 14:1 - Line 24:16), packages/frontend/src/types/userProfile.ts (Line 1:1 - Line 11:94)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn182" onclick="toggleCodeBlock('cloneGroup182', 'expandBtn182', 'collapseBtn182')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn182" onclick="toggleCodeBlock('cloneGroup182', 'expandBtn182', 'collapseBtn182')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup182"><code class="language-typescript text-sm text-gray-800">export interface UserProfile {
  user_id: string;
  username: string | null;
  full_name: string | null;
  title: string | null;
  organization: string | null;
  bio: string | null;
  location: string | null;
  avatar_url: string | null;
  preferred_language: string | null;
  preferred_theme</code></pre></div><div class="py-4"><p class="text-gray-600">packages/types/src/user.d.ts (Line 1:1 - Line 46:2), packages/types/src/user.ts (Line 1:1 - Line 53:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn183" onclick="toggleCodeBlock('cloneGroup183', 'expandBtn183', 'collapseBtn183')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn183" onclick="toggleCodeBlock('cloneGroup183', 'expandBtn183', 'collapseBtn183')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup183"><code class="language-typescript text-sm text-gray-800">/**
 * User and authentication related types
 */
export interface User {
  id: string;
  email: string;
  name?: string;
  role?: 'user' | 'admin';
  created_at?: string;
  updated_at?: string;
}
export interface UserProfile {
  user_id: string;
  username: string | null;
  full_name: string | null;
  title: string | null;
  organization: string | null;
  bio: string | null;
  location: string | null;
  avatar_url: string | null;
  preferred_language: string | null;
  preferred_theme?: 'light' | 'dark' | 'system' | null;
  storage_limit_bytes?: number | null;
  storage_used_bytes?: number | null;
}
export interface LoginResponse {
  token: string;
  user: User;
}
export interface RefreshTokenResponse {
  token: string;
  expires_at: string;
}
export type UserProfileUpdatePayload = Partial&lt;Omit&lt;UserProfile, 'user_id'&gt;&gt;;
export interface AccessRequestPayload {
  email: string;
  name?: string;
  organization?: string;
  reason?: string;
}
export interface AccessRequestResponse {
  id: string;
  email: string;
  status: string;
  created_at: string;
}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/types/src/segmentation.d.ts (Line 6:2 - Line 59:2), packages/types/src/segmentation.ts (Line 9:2 - Line 67:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn184" onclick="toggleCodeBlock('cloneGroup184', 'expandBtn184', 'collapseBtn184')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn184" onclick="toggleCodeBlock('cloneGroup184', 'expandBtn184', 'collapseBtn184')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup184"><code class="language-typescript text-sm text-gray-800">enum SegmentationStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SAVING = 'saving',
}
export interface SegmentationData {
  id: string;
  image_id?: string;
  imageId?: string;
  status?: ImageStatus;
  result_data?: {
    polygons: Polygon[];
    [key: string]: unknown;
  };
  polygons: Polygon[];
  created_at?: string;
  updated_at?: string;
  parameters?: {
    model?: string;
    threshold?: number;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}
export interface SegmentationResult {
  id: string;
  polygons: Polygon[];
  [key: string]: unknown;
}
export interface SegmentationResultData {
  polygons?: Polygon[];
  contours?: Array&lt;Array&lt;[number, number]&gt;&gt;;
  hierarchy?: Array&lt;[number, number, number, number]&gt;;
  imageWidth: number;
  imageHeight: number;
  metadata?: {
    source?: 'resunet' | 'api' | 'empty' | 'cv2';
    timestamp?: string;
    modelType?: string;
    [key: string]: unknown;
  };
}
export interface SegmentationApiResponse {
  image_id: string;
  status: ImageStatus;
  result_data?: SegmentationResultData | null;
  parameters?: Record&lt;string, unknown&gt; | null;
  created_at: string;
  updated_at: string;
  error?: string | null;
}
export type CanvasSegmentationData = SegmentationResultData;</code></pre></div><div class="py-4"><p class="text-gray-600">packages/types/src/project.d.ts (Line 1:1 - Line 30:2), packages/types/src/project.ts (Line 1:1 - Line 33:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn185" onclick="toggleCodeBlock('cloneGroup185', 'expandBtn185', 'collapseBtn185')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn185" onclick="toggleCodeBlock('cloneGroup185', 'expandBtn185', 'collapseBtn185')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup185"><code class="language-typescript text-sm text-gray-800">/**
 * Project related types
 */
export interface Project {
  id: string;
  user_id: string;
  title: string;
  description: string | null;
  created_at: string;
  updated_at: string;
  thumbnail_url?: string | null;
  image_count?: number;
}
export type ProjectCreatePayload = Pick&lt;Project, 'title' | 'description'&gt;;
export interface ProjectStatsResponse {
  user_id: string;
  project_count: number;
  image_count: number;
  segmentation_count: number;
  recently_updated_projects: Array&lt;{
    id: string;
    title: string;
    updated_at: string;
  }&gt;;
  storage_usage: {
    total_bytes: number;
    images_bytes: number;
    segmentations_bytes: number;
  };
}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/types/src/polygon.d.ts (Line 1:1 - Line 44:8), packages/types/src/polygon.ts (Line 1:1 - Line 52:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn186" onclick="toggleCodeBlock('cloneGroup186', 'expandBtn186', 'collapseBtn186')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn186" onclick="toggleCodeBlock('cloneGroup186', 'expandBtn186', 'collapseBtn186')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup186"><code class="language-typescript text-sm text-gray-800">/**
 * Types related to polygons and geometric objects
 */
export interface Point {
  x: number;
  y: number;
}
export interface Polygon {
  id: string;
  points: Point[];
  type?: 'external' | 'internal';
  class?: string;
  color?: string;
  parentId?: string;
  [key: string]: unknown;
}
export interface VertexHoverInfo {
  polygonId: string | null;
  vertexIndex: number | null;
}
export interface VertexDragInfo {
  polygonId: string | null;
  vertexIndex: number | null;
  isDragging: boolean;
}
export interface DragInfo {
  isDragging: boolean;
  startX: number;
  startY: number;
  lastX: number;
  lastY: number;
}
export interface TempPointsInfo {
  points: Point[];
  polygonId?: string | null;
  startIndex?: number | null;
  endIndex?: number | null;
}
export interface TransformState {
  zoom: number;
  translateX: number;
  translateY: number;
}
export declare</code></pre></div><div class="py-4"><p class="text-gray-600">packages/types/src/polygon.d.ts (Line 44:2 - Line 60:2), packages/types/src/polygon.ts (Line 52:2 - Line 69:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn187" onclick="toggleCodeBlock('cloneGroup187', 'expandBtn187', 'collapseBtn187')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn187" onclick="toggleCodeBlock('cloneGroup187', 'expandBtn187', 'collapseBtn187')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup187"><code class="language-typescript text-sm text-gray-800">enum EditMode {
  View = 'VIEW',
  Edit = 'EDIT',
  Slice = 'SLICE',
  PointAdding = 'POINT_ADDING',
}
export interface InteractionState {
  isDraggingVertex: boolean;
  isPanning: boolean;
  panStart: Point | null;
  draggedVertexInfo: {
    polygonId: string;
    vertexIndex: number;
    originalPoint: Point;
  } | null;
  sliceStartPoint: Point | null;
}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/types/src/image.d.ts (Line 1:1 - Line 51:2), packages/types/src/image.ts (Line 1:1 - Line 55:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn188" onclick="toggleCodeBlock('cloneGroup188', 'expandBtn188', 'collapseBtn188')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn188" onclick="toggleCodeBlock('cloneGroup188', 'expandBtn188', 'collapseBtn188')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup188"><code class="language-typescript text-sm text-gray-800">/**
 * Image related types
 */
import { ImageStatus } from './segmentation';
export interface ImageData {
  id: string;
  name: string;
  width: number;
  height: number;
  src: string;
  storage_path?: string;
  storage_path_full?: string;
  project_id?: string;
  projectId?: string;
  user_id?: string;
  created_at?: string;
  updated_at?: string;
  status?: ImageStatus;
  actualId?: string;
  [key: string]: unknown;
}
export interface Image {
  id: string;
  project_id: string;
  user_id: string;
  name: string;
  storage_path: string;
  thumbnail_path: string | null;
  width: number | null;
  height: number | null;
  metadata: Record&lt;string, unknown&gt; | null;
  status: ImageStatus;
  created_at: string;
  updated_at: string;
  segmentation_result?: {
    path?: string | null;
  } | null;
}
export interface ProjectImage {
  id: string;
  project_id: string;
  name: string;
  url: string;
  thumbnail_url: string | null;
  createdAt: Date;
  updatedAt: Date;
  width: number | null;
  height: number | null;
  segmentationStatus: ImageStatus;
  segmentationResultPath?: string | null;
}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/config.ts (Line 32:12 - Line 38:15), packages/backend/src/config/index.ts (Line 67:15 - Line 73:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn190" onclick="toggleCodeBlock('cloneGroup190', 'expandBtn190', 'collapseBtn190')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn190" onclick="toggleCodeBlock('cloneGroup190', 'expandBtn190', 'collapseBtn190')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup190"><code class="language-typescript text-sm text-gray-800">,
  port: parseInt(process.env.DB_PORT || '5432', 10),
  database: process.env.DB_NAME || 'spheroseg',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  ssl: process.env.DB_SSL === 'true',
  maxConnections</code></pre></div><!-- Add more clone groups for .txt format as needed         // Add more clone groups for .txt format as needed--></div><a name="tsx-clones"></a><h2 class="text-2xl font-semibold text-gray-700 mb-4">tsx</h2><div class="divide-y divide-gray-200 border-b-2"><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 93:3 - Line 106:7), packages/frontend/src/pages/segmentation/hooks/polygonInteraction/editMode/useTempPoints.tsx (Line 47:3 - Line 60:9)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn4" onclick="toggleCodeBlock('cloneGroup4', 'expandBtn4', 'collapseBtn4')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn4" onclick="toggleCodeBlock('cloneGroup4', 'expandBtn4', 'collapseBtn4')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup4"><code class="language-tsx text-sm text-gray-800">useEffect(() =&gt; {
    const handleKeyDown = (e: KeyboardEvent) =&gt; {
      if (e.key === 'Shift') {
        setIsShiftPressed(true);
      }
    };

    const handleKeyUp = (e: KeyboardEvent) =&gt; {
      if (e.key === 'Shift') {
        setIsShiftPressed(false);
      }
    };

    window</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 816:17 - Line 828:64), packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 722:13 - Line 734:42)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn5" onclick="toggleCodeBlock('cloneGroup5', 'expandBtn5', 'collapseBtn5')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn5" onclick="toggleCodeBlock('cloneGroup5', 'expandBtn5', 'collapseBtn5')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup5"><code class="language-tsx text-sm text-gray-800">${index}`}
                    cx={point.x}
                    cy={point.y}
                    r={index === 0 ? vertexRadius * 1.5 : vertexRadius} // Make first point larger
                    fill={index === 0 ? 'yellow' : 'cyan'} // Make first point a different color
                    stroke=&quot;black&quot;
                    strokeWidth={1 / transform.zoom}
                    vectorEffect=&quot;non-scaling-stroke&quot;
                    style={{ pointerEvents: 'none' }}
                  /&gt;
                ))}

              {/* Draw line from start vertex to first temp point or cursor */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/components/canvas/CanvasPolygon.tsx (Line 32:3 - Line 39:16), packages/frontend/src/pages/segmentation/components/canvas/PolygonCollection.tsx (Line 19:3 - Line 26:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn6" onclick="toggleCodeBlock('cloneGroup6', 'expandBtn6', 'collapseBtn6')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn6" onclick="toggleCodeBlock('cloneGroup6', 'expandBtn6', 'collapseBtn6')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup6"><code class="language-tsx text-sm text-gray-800">editMode: EditMode;
  onSelectPolygon?: (id: string) =&gt; void;
  onDeletePolygon?: (id: string) =&gt; void;
  onSlicePolygon?: (id: string) =&gt; void;
  onEditPolygon?: (id: string) =&gt; void;
  onDeleteVertex?: (polygonId: string, vertexIndex: number) =&gt; void;
  onDuplicateVertex?: (polygonId: string, vertexIndex: number) =&gt; void;
  relatedPolygons</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/__tests__/keyboard/SegmentationEditorV2KeyboardTests.tsx (Line 17:3 - Line 39:2), packages/frontend/src/pages/segmentation/__tests__/polygon/SegmentationEditorV2PolygonTests.tsx (Line 16:3 - Line 38:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn7" onclick="toggleCodeBlock('cloneGroup7', 'expandBtn7', 'collapseBtn7')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn7" onclick="toggleCodeBlock('cloneGroup7', 'expandBtn7', 'collapseBtn7')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup7"><code class="language-tsx text-sm text-gray-800">const mockState = {
    imageData: {
      id: 'test-image-id',
      actualId: 'test-image-id',
      name: 'test-image.jpg',
      url: 'https://example.com/test-image.jpg',
      width: 800,
      height: 600,
    },
    segmentationData: {
      polygons: [
        {
          id: 'polygon-1',
          points: [
            { x: 100, y: 100 },
            { x: 200, y: 100 },
            { x: 200, y: 200 },
            { x: 100, y: 200 },
          ],
          color: '#FF0000',
          label: 'Cell 1',
        },
      ]</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/__tests__/keyboard/SegmentationEditorV2KeyboardTests.tsx (Line 59:31 - Line 66:2), packages/frontend/src/pages/segmentation/__tests__/polygon/SegmentationEditorV2PolygonTests.tsx (Line 79:17 - Line 86:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn8" onclick="toggleCodeBlock('cloneGroup8', 'expandBtn8', 'collapseBtn8')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn8" onclick="toggleCodeBlock('cloneGroup8', 'expandBtn8', 'collapseBtn8')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup8"><code class="language-tsx text-sm text-gray-800">: vi.fn(),
    handleSave: vi.fn(),
    undo: vi.fn(),
    redo: vi.fn(),
    onMouseDown: vi.fn(),
    onMouseMove: vi.fn(),
    onMouseUp: vi.fn(),
    getCanvasCoordinates: vi.fn()</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/__tests__/keyboard/SegmentationEditorV2KeyboardTests.tsx (Line 66:2 - Line 85:24), packages/frontend/src/pages/segmentation/__tests__/polygon/SegmentationEditorV2PolygonTests.tsx (Line 86:2 - Line 105:48)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn9" onclick="toggleCodeBlock('cloneGroup9', 'expandBtn9', 'collapseBtn9')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn9" onclick="toggleCodeBlock('cloneGroup9', 'expandBtn9', 'collapseBtn9')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup9"><code class="language-tsx text-sm text-gray-800">),
    handleDeletePolygon: vi.fn(),
  };

  return {
    useSegmentationV2: vi.fn(() =&gt; mockState),
    EditMode: {
      View: 'View',
      EditVertices: 'EditVertices',
      AddPolygon: 'AddPolygon',
      DeletePolygon: 'DeletePolygon',
      SlicePolygon: 'SlicePolygon',
      MergePolygons: 'MergePolygons',
    },
    // Export the mock state so we can modify it during tests
    _mockSegmentationState: mockState,
  };
});

// Mock useSlicing hook</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/__tests__/keyboard/SegmentationEditorV2KeyboardTests.tsx (Line 90:2 - Line 101:27), packages/frontend/src/pages/segmentation/__tests__/polygon/SegmentationEditorV2PolygonTests.tsx (Line 146:2 - Line 157:63)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn10" onclick="toggleCodeBlock('cloneGroup10', 'expandBtn10', 'collapseBtn10')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn10" onclick="toggleCodeBlock('cloneGroup10', 'expandBtn10', 'collapseBtn10')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup10"><code class="language-tsx text-sm text-gray-800">);

// Mock react-router-dom's useNavigate
vi.mock('react-router-dom', async () =&gt; {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(() =&gt; vi.fn()),
  };
});

// Mock CanvasV2 component</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/__tests__/keyboard/SegmentationEditorV2KeyboardTests.tsx (Line 116:2 - Line 137:45), packages/frontend/src/pages/segmentation/__tests__/polygon/SegmentationEditorV2PolygonTests.tsx (Line 231:2 - Line 252:44)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn11" onclick="toggleCodeBlock('cloneGroup11', 'expandBtn11', 'collapseBtn11')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn11" onclick="toggleCodeBlock('cloneGroup11', 'expandBtn11', 'collapseBtn11')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup11"><code class="language-tsx text-sm text-gray-800">),
}));

// Mock toast
vi.mock('sonner', () =&gt; ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock react-i18next
vi.mock('react-i18next', () =&gt; ({
  useTranslation: () =&gt; ({
    t: (key: string) =&gt; key,
  }),
}));

// Mock fetch for API calls
global.fetch = vi.fn();

describe('SegmentationEditorV2 Keyboard Interactions'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/__tests__/keyboard/SegmentationEditorV2KeyboardTests.tsx (Line 137:45 - Line 158:3), packages/frontend/src/pages/segmentation/__tests__/polygon/SegmentationEditorV2PolygonTests.tsx (Line 252:44 - Line 273:28)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn12" onclick="toggleCodeBlock('cloneGroup12', 'expandBtn12', 'collapseBtn12')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn12" onclick="toggleCodeBlock('cloneGroup12', 'expandBtn12', 'collapseBtn12')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup12"><code class="language-tsx text-sm text-gray-800">, () =&gt; {
  beforeEach(() =&gt; {
    // Setup all context mocks
    setupAllContextMocks();

    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() =&gt; {
    vi.clearAllMocks();
  });

  const renderComponent = () =&gt; {
    return render(
      &lt;MemoryRouterWrapper&gt;
        &lt;SegmentationEditorV2 projectId=&quot;test-project-id&quot; imageId=&quot;test-image-id&quot; /&gt;
      &lt;/MemoryRouterWrapper&gt;,
    );
  };

  it</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ui/enhanced-file-uploader.tsx (Line 119:2 - Line 140:3), packages/frontend/src/components/ui/file-uploader.tsx (Line 64:8 - Line 85:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn36" onclick="toggleCodeBlock('cloneGroup36', 'expandBtn36', 'collapseBtn36')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn36" onclick="toggleCodeBlock('cloneGroup36', 'expandBtn36', 'collapseBtn36')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup36"><code class="language-tsx text-sm text-gray-800">],
  );

  // Convert accept string to the format expected by react-dropzone v14+
  const acceptProp =
    typeof accept === 'string'
      ? { [accept]: [] } // Convert 'image/*' to { 'image/*': [] }
      : accept;

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: acceptProp,
    multiple,
    maxSize,
    disabled: disabled || uploadState === 'uploading',
    onDrop: handleDrop,
    onDragEnter: () =&gt; setDragActive(true),
    onDragLeave: () =&gt; setDragActive(false),
  });

  // Handle file removal
  const removeFile = (index: number) =&gt; {
    if</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ui/enhanced-file-uploader.tsx (Line 239:2 - Line 244:2), packages/frontend/src/components/ui/file-uploader.tsx (Line 167:2 - Line 172:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn37" onclick="toggleCodeBlock('cloneGroup37', 'expandBtn37', 'collapseBtn37')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn37" onclick="toggleCodeBlock('cloneGroup37', 'expandBtn37', 'collapseBtn37')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup37"><code class="language-tsx text-sm text-gray-800">(
        &lt;div className=&quot;flex-1 min-w-0&quot;&gt;
          &lt;p className=&quot;text-sm font-medium text-gray-900 dark:text-gray-100 truncate&quot;&gt;{file.name}&lt;/p&gt;
          &lt;p className=&quot;text-xs text-gray-500 dark:text-gray-400&quot;&gt;{(file.size / 1024).toFixed(2)} kB&lt;/p&gt;
        &lt;/div&gt;
      );</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ui/enhanced-file-uploader.tsx (Line 321:13 - Line 335:3), packages/frontend/src/components/ui/file-uploader.tsx (Line 120:10 - Line 134:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn38" onclick="toggleCodeBlock('cloneGroup38', 'expandBtn38', 'collapseBtn38')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn38" onclick="toggleCodeBlock('cloneGroup38', 'expandBtn38', 'collapseBtn38')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup38"><code class="language-tsx text-sm text-gray-800">}&lt;/span&gt;
        &lt;/div&gt;
      );
    }

    return null;
  };

  // File preview section
  const renderPreview = () =&gt; {
    if (!showPreview || files.length === 0) return null;

    return (
      &lt;div
        className={cn</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ui/enhanced-file-uploader.tsx (Line 404:2 - Line 414:16), packages/frontend/src/components/ui/file-uploader.tsx (Line 212:12 - Line 223:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn39" onclick="toggleCodeBlock('cloneGroup39', 'expandBtn39', 'collapseBtn39')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn39" onclick="toggleCodeBlock('cloneGroup39', 'expandBtn39', 'collapseBtn39')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup39"><code class="language-tsx text-sm text-gray-800">;
  };

  return (
    &lt;div className={cn('space-y-4', className)}&gt;
      &lt;div {...getRootProps()} className={getContainerClasses()}&gt;
        &lt;input {...getInputProps()} /&gt;

        &lt;div className=&quot;p-8 text-center&quot;&gt;
          &lt;UploadCloud className=&quot;mx-auto h-12 w-12 text-gray-400&quot; /&gt;
          &lt;p className=&quot;mt-2 text-sm font-medium text-gray-700 dark:text-gray-300&quot;&gt;{getDropzoneText</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/SegmentationProgress.tsx (Line 444:11 - Line 454:53), packages/frontend/src/components/project/SegmentationProgress.tsx (Line 211:9 - Line 221:53)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn44" onclick="toggleCodeBlock('cloneGroup44', 'expandBtn44', 'collapseBtn44')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn44" onclick="toggleCodeBlock('cloneGroup44', 'expandBtn44', 'collapseBtn44')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup44"><code class="language-tsx text-sm text-gray-800">const normalizeQueueStatusData = (rawData: any) =&gt; {
            // Získáme vlastní data z odpovědi (může být v data.data nebo přímo v data)
            const responseData = rawData.data || rawData;

            // Kontrola zda obsahuje runningTasks nebo aktivní úlohy
            const running = responseData.runningTasks || responseData.activeTasks || [];

            // Kontrola zda obsahuje queuedTasks, pendingTasks, nebo queuedImages
            const queued = responseData.queuedTasks || responseData.pendingTasks || [];

            // Zachováme originální pendingTasks, pokud existují</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/SegmentationProgress.tsx (Line 461:2 - Line 473:88), packages/frontend/src/components/project/SegmentationProgress.tsx (Line 222:2 - Line 234:50)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn45" onclick="toggleCodeBlock('cloneGroup45', 'expandBtn45', 'collapseBtn45')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn45" onclick="toggleCodeBlock('cloneGroup45', 'expandBtn45', 'collapseBtn45')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup45"><code class="language-tsx text-sm text-gray-800">|| [];

            // Pokud máme běžící úlohy, ale nemáme žádné processingImages, vytvoříme zástupné položky
            const enhancedProcessingImages =
              running.length &gt; 0 &amp;&amp; processingImages.length === 0
                ? running.map((taskId: string) =&gt; ({
                    id: taskId,
                    name: `Processing ${taskId.substring(0, 8)}...`,
                    projectId: (responseData.projectId || projectId || 'unknown'),
                  }))
                : processingImages;

            // Pokud máme čekající úlohy, ale nemáme žádné queuedImages, vytvoříme zástupné položky</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ProjectToolbar.tsx (Line 95:2 - Line 106:63), packages/frontend/src/components/project/ProjectToolbar.tsx (Line 69:11 - Line 80:13)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn46" onclick="toggleCodeBlock('cloneGroup46', 'expandBtn46', 'collapseBtn46')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn46" onclick="toggleCodeBlock('cloneGroup46', 'expandBtn46', 'collapseBtn46')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup46"><code class="language-tsx text-sm text-gray-800">&gt;
            &lt;svg
              xmlns=&quot;http://www.w3.org/2000/svg&quot;
              width=&quot;16&quot;
              height=&quot;16&quot;
              viewBox=&quot;0 0 24 24&quot;
              fill=&quot;none&quot;
              stroke=&quot;currentColor&quot;
              strokeWidth=&quot;2&quot;
              strokeLinecap=&quot;round&quot;
              strokeLinejoin=&quot;round&quot;
              className=&quot;absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ProjectToolbar.tsx (Line 122:2 - Line 135:5), packages/frontend/src/components/project/ProjectToolbar.tsx (Line 69:11 - Line 82:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn47" onclick="toggleCodeBlock('cloneGroup47', 'expandBtn47', 'collapseBtn47')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn47" onclick="toggleCodeBlock('cloneGroup47', 'expandBtn47', 'collapseBtn47')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup47"><code class="language-tsx text-sm text-gray-800">&gt;
            &lt;svg
              xmlns=&quot;http://www.w3.org/2000/svg&quot;
              width=&quot;16&quot;
              height=&quot;16&quot;
              viewBox=&quot;0 0 24 24&quot;
              fill=&quot;none&quot;
              stroke=&quot;currentColor&quot;
              strokeWidth=&quot;2&quot;
              strokeLinecap=&quot;round&quot;
              strokeLinejoin=&quot;round&quot;
              className=&quot;mr-1 h-4 w-4&quot;
            &gt;
              &lt;path</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ProjectToolbar.tsx (Line 144:2 - Line 159:17), packages/frontend/src/components/project/ProjectToolbar.tsx (Line 121:2 - Line 136:14)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn48" onclick="toggleCodeBlock('cloneGroup48', 'expandBtn48', 'collapseBtn48')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn48" onclick="toggleCodeBlock('cloneGroup48', 'expandBtn48', 'collapseBtn48')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup48"><code class="language-tsx text-sm text-gray-800">&amp;&amp; (
          &lt;Button variant=&quot;outline&quot; size=&quot;sm&quot; className=&quot;flex items-center h-9&quot; onClick={handleExport}&gt;
            &lt;svg
              xmlns=&quot;http://www.w3.org/2000/svg&quot;
              width=&quot;16&quot;
              height=&quot;16&quot;
              viewBox=&quot;0 0 24 24&quot;
              fill=&quot;none&quot;
              stroke=&quot;currentColor&quot;
              strokeWidth=&quot;2&quot;
              strokeLinecap=&quot;round&quot;
              strokeLinejoin=&quot;round&quot;
              className=&quot;mr-1 h-4 w-4&quot;
            &gt;
              &lt;path d=&quot;M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4&quot; /&gt;
              &lt;polyline points=&quot;7 10 12 15 17 10</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ProjectToolbar.tsx (Line 210:26 - Line 223:23), packages/frontend/src/components/project/ProjectToolbar.tsx (Line 67:22 - Line 80:13)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn49" onclick="toggleCodeBlock('cloneGroup49', 'expandBtn49', 'collapseBtn49')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn49" onclick="toggleCodeBlock('cloneGroup49', 'expandBtn49', 'collapseBtn49')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup49"><code class="language-tsx text-sm text-gray-800">&quot;
            onClick={() =&gt; setViewMode('grid')}
          &gt;
            &lt;svg
              xmlns=&quot;http://www.w3.org/2000/svg&quot;
              width=&quot;16&quot;
              height=&quot;16&quot;
              viewBox=&quot;0 0 24 24&quot;
              fill=&quot;none&quot;
              stroke=&quot;currentColor&quot;
              strokeWidth=&quot;2&quot;
              strokeLinecap=&quot;round&quot;
              strokeLinejoin=&quot;round&quot;
              className=&quot;lucide lucide-grid-2x2</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ProjectToolbar.tsx (Line 233:26 - Line 246:19), packages/frontend/src/components/project/ProjectToolbar.tsx (Line 67:22 - Line 80:13)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn50" onclick="toggleCodeBlock('cloneGroup50', 'expandBtn50', 'collapseBtn50')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn50" onclick="toggleCodeBlock('cloneGroup50', 'expandBtn50', 'collapseBtn50')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup50"><code class="language-tsx text-sm text-gray-800">&quot;
            onClick={() =&gt; setViewMode('list')}
          &gt;
            &lt;svg
              xmlns=&quot;http://www.w3.org/2000/svg&quot;
              width=&quot;16&quot;
              height=&quot;16&quot;
              viewBox=&quot;0 0 24 24&quot;
              fill=&quot;none&quot;
              stroke=&quot;currentColor&quot;
              strokeWidth=&quot;2&quot;
              strokeLinecap=&quot;round&quot;
              strokeLinejoin=&quot;round&quot;
              className=&quot;lucide lucide-list</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ProjectThumbnail.tsx (Line 31:11 - Line 54:8), packages/frontend/src/components/project/SegmentationThumbnail.tsx (Line 388:7 - Line 411:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn51" onclick="toggleCodeBlock('cloneGroup51', 'expandBtn51', 'collapseBtn51')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn51" onclick="toggleCodeBlock('cloneGroup51', 'expandBtn51', 'collapseBtn51')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup51"><code class="language-tsx text-sm text-gray-800">e.preventDefault();
          return;
        }

        try {
          // Only try direct URL once, and only if we're not already showing fallback
          if (!triedDirectUrl &amp;&amp; thumbnailUrl &amp;&amp; !thumbnailUrl.startsWith('blob:')) {
            setTriedDirectUrl(true);

            const backendUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
            const thumbnailPath =
              thumbnailUrl &amp;&amp; thumbnailUrl.includes('uploads/')
                ? thumbnailUrl.substring(thumbnailUrl.indexOf('uploads/') + 8)
                : thumbnailUrl || '';

            // Only try backendUrl if thumbnailPath is valid
            if (thumbnailPath &amp;&amp; thumbnailPath.length &gt; 0) {
              const directPath = `${backendUrl}/uploads/${thumbnailPath}`;
              e.currentTarget.src = directPath;
              return;
            }
          }
        } catch (err) {
          console</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ProjectImages.tsx (Line 194:7 - Line 201:47), packages/frontend/src/components/project/ProjectImages.tsx (Line 157:9 - Line 164:47)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn52" onclick="toggleCodeBlock('cloneGroup52', 'expandBtn52', 'collapseBtn52')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn52" onclick="toggleCodeBlock('cloneGroup52', 'expandBtn52', 'collapseBtn52')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup52"><code class="language-tsx text-sm text-gray-800">&gt;
        {localImages.map((image) =&gt; {
          // Ensure each image has a valid ID for the key prop
          const imageId = image.id || `img-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

          // Log any images without IDs for debugging
          if (!image.id) {
            console.warn('Image missing ID in ProjectImages list view:'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ProjectImages.tsx (Line 206:15 - Line 220:2), packages/frontend/src/components/project/ProjectImages.tsx (Line 169:17 - Line 185:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn53" onclick="toggleCodeBlock('cloneGroup53', 'expandBtn53', 'collapseBtn53')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn53" onclick="toggleCodeBlock('cloneGroup53', 'expandBtn53', 'collapseBtn53')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup53"><code class="language-tsx text-sm text-gray-800">key={imageId}
              image={{ ...image, id: imageId }} // Ensure image has an ID
              onDelete={onDelete}
              onOpen={selectionMode ? undefined : onOpen}
              onResegment={handleResegment}
              selectionMode={selectionMode}
              isSelected={!!selectedImages[imageId]}
              onToggleSelection={(event) =&gt; onToggleSelection?.(imageId, event)}
            /&gt;
          );
        })}
      &lt;/motion.div&gt;
    &lt;/&gt;
  );
};</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ProjectImageProcessor.tsx (Line 83:25 - Line 90:17), packages/frontend/src/components/project/ProjectImageProcessor.tsx (Line 45:32 - Line 52:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn54" onclick="toggleCodeBlock('cloneGroup54', 'expandBtn54', 'collapseBtn54')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn54" onclick="toggleCodeBlock('cloneGroup54', 'expandBtn54', 'collapseBtn54')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup54"><code class="language-tsx text-sm text-gray-800">;
      if (axios.isAxiosError(error) &amp;&amp; error.response) {
        message = error.response.data?.message || message;
      } else if (error instanceof Error) {
        message = error.message;
      }
      toast.error(message);
      setLoadingStatus</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ProjectImageActions.tsx (Line 163:25 - Line 177:2), packages/frontend/src/components/project/ProjectImageActions.tsx (Line 75:25 - Line 90:55)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn55" onclick="toggleCodeBlock('cloneGroup55', 'expandBtn55', 'collapseBtn55')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn55" onclick="toggleCodeBlock('cloneGroup55', 'expandBtn55', 'collapseBtn55')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup55"><code class="language-tsx text-sm text-gray-800">const { projectImagesCache } = await import('@/api/projectImages');

                        // Update cache for both key formats
                        [cleanProjectId, cleanProjectId].forEach((cacheKey) =&gt; {
                          if (projectImagesCache &amp;&amp; projectImagesCache[cacheKey]) {
                            projectImagesCache[cacheKey].data = projectImagesCache[cacheKey].data.filter(
                              (img: any) =&gt; img.id !== imageId,
                            );
                            console.log(`Updated cache for project ${cacheKey}`);
                          }
                        });
                      } catch (cacheError) {
                        console.error('Error updating image cache:', cacheError);
                      }
                    }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ProjectImageActions.tsx (Line 560:35 - Line 578:4), packages/frontend/src/components/project/ProjectImageActions.tsx (Line 536:67 - Line 554:10)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn56" onclick="toggleCodeBlock('cloneGroup56', 'expandBtn56', 'collapseBtn56')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn56" onclick="toggleCodeBlock('cloneGroup56', 'expandBtn56', 'collapseBtn56')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup56"><code class="language-tsx text-sm text-gray-800">);

          // Vrátíme status na původní
          try {
            onImagesChange(
              images.map((img: ProjectImage) =&gt; {
                if (img.id === imageId) {
                  return {
                    ...img,
                    segmentationStatus: 'pending' as ImageStatus,
                  }; // Reset to pending or original status
                }
                return img;
              }),
            );
          } catch (error) {
            console.warn('Failed to update images with onImagesChange:', error);
          }
          throw new</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ImageCard.tsx (Line 59:55 - Line 72:83), packages/frontend/src/components/project/ImageListItem.tsx (Line 56:59 - Line 68:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn58" onclick="toggleCodeBlock('cloneGroup58', 'expandBtn58', 'collapseBtn58')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn58" onclick="toggleCodeBlock('cloneGroup58', 'expandBtn58', 'collapseBtn58')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup58"><code class="language-tsx text-sm text-gray-800">${data.imageId}`);

          // Use setTimeout to ensure the status update is processed first
          setTimeout(() =&gt; {
            const queueUpdateEvent = new CustomEvent('queue-status-update', {
              detail: {
                refresh: true,
                forceRefresh: true,
                immediate: data.status === 'processing',
              },
            });
            window.dispatchEvent(queueUpdateEvent);

            // Also dispatch an image-status-update event to ensure all components are updated</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ImageCard.tsx (Line 182:14 - Line 195:59), packages/frontend/src/components/project/ImageListItem.tsx (Line 92:11 - Line 105:63)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn59" onclick="toggleCodeBlock('cloneGroup59', 'expandBtn59', 'collapseBtn59')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn59" onclick="toggleCodeBlock('cloneGroup59', 'expandBtn59', 'collapseBtn59')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup59"><code class="language-tsx text-sm text-gray-800">]);

  // Listen for custom events (fallback mechanism)
  useEffect(() =&gt; {
    const handleImageStatusUpdate = (event: Event) =&gt; {
      const customEvent = event as CustomEvent&lt;{
        imageId: string;
        status: string;
        forceQueueUpdate?: boolean;
      }&gt;;
      const { imageId, status, forceQueueUpdate } = customEvent.detail;

      if (imageId === image.id) {
        console.log(`ImageCard: Received custom event status update for image </code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ImageCard.tsx (Line 202:55 - Line 226:58), packages/frontend/src/components/project/ImageListItem.tsx (Line 110:59 - Line 133:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn60" onclick="toggleCodeBlock('cloneGroup60', 'expandBtn60', 'collapseBtn60')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn60" onclick="toggleCodeBlock('cloneGroup60', 'expandBtn60', 'collapseBtn60')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup60"><code class="language-tsx text-sm text-gray-800">${imageId}`);

          // Use setTimeout to ensure the status update is processed first
          setTimeout(() =&gt; {
            const queueUpdateEvent = new CustomEvent('queue-status-update', {
              detail: {
                refresh: true,
                forceRefresh: true,
                immediate: status === 'processing',
              },
            });
            window.dispatchEvent(queueUpdateEvent);
          }, 100);
        }
      }
    };

    window.addEventListener('image-status-update', handleImageStatusUpdate);

    return () =&gt; {
      window.removeEventListener('image-status-update', handleImageStatusUpdate);
    };
  }, [image.id]);

  // Try to load image from IndexedDB when component mounts</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ImageCard.tsx (Line 340:101 - Line 352:36), packages/frontend/src/components/project/ImageListItem.tsx (Line 141:151 - Line 153:63)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn61" onclick="toggleCodeBlock('cloneGroup61', 'expandBtn61', 'collapseBtn61')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn61" onclick="toggleCodeBlock('cloneGroup61', 'expandBtn61', 'collapseBtn61')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup61"><code class="language-tsx text-sm text-gray-800">,
          isSelected ? 'ring-2 ring-blue-500' : '',
          className,
        )}
        onClick={(e) =&gt; {
          if (selectionMode) {
            onToggleSelection?.(e);
          } else if (onOpen) {
            onOpen(image.id);
          }
        }}
      &gt;
        {/* Selection checkbox or actions */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ImageCard.tsx (Line 453:15 - Line 462:3), packages/frontend/src/components/project/ImageListItem.tsx (Line 240:3 - Line 249:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn62" onclick="toggleCodeBlock('cloneGroup62', 'expandBtn62', 'collapseBtn62')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn62" onclick="toggleCodeBlock('cloneGroup62', 'expandBtn62', 'collapseBtn62')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup62"><code class="language-tsx text-sm text-gray-800">)}
            &lt;/p&gt;
            {currentStatus === 'failed' &amp;&amp; image.error &amp;&amp; (
              &lt;div className=&quot;mt-1 text-xs text-red-500 truncate&quot; title={image.error}&gt;
                &lt;span className=&quot;font-medium&quot;&gt;Chyba:&lt;/span&gt; {image.error.substring(0, 50)}
                {image.error.length &gt; 50 ? '...' : ''}
              &lt;/div&gt;
            )}
          &lt;/div&gt;
        &lt;/</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ImageActions.tsx (Line 15:5 - Line 43:13), packages/frontend/src/components/project/ImageListActions.tsx (Line 15:8 - Line 43:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn63" onclick="toggleCodeBlock('cloneGroup63', 'expandBtn63', 'collapseBtn63')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn63" onclick="toggleCodeBlock('cloneGroup63', 'expandBtn63', 'collapseBtn63')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup63"><code class="language-tsx text-sm text-gray-800">&quot;
          size=&quot;icon&quot;
          className=&quot;h-7 w-7&quot;
          onClick={(e) =&gt; {
            e.stopPropagation();
            onResegment();
          }}
          title=&quot;Opětovná segmentace&quot;
        &gt;
          &lt;RefreshCcw className=&quot;h-4 w-4&quot; /&gt;
        &lt;/Button&gt;
      )}
      &lt;Button
        variant=&quot;destructive&quot;
        size=&quot;icon&quot;
        className=&quot;h-7 w-7&quot;
        onClick={(e) =&gt; {
          e.stopPropagation();
          onDelete();
        }}
        title=&quot;Smazat obrázek&quot;
      &gt;
        &lt;Trash2 className=&quot;h-4 w-4&quot; /&gt;
      &lt;/Button&gt;
    &lt;/div&gt;
  );
};

export default ImageActions</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/__tests__/ImageUploaderTest.tsx (Line 105:22 - Line 116:22), packages/frontend/src/components/__tests__/ImageUploaderTest.tsx (Line 85:25 - Line 96:66)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn64" onclick="toggleCodeBlock('cloneGroup64', 'expandBtn64', 'collapseBtn64')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn64" onclick="toggleCodeBlock('cloneGroup64', 'expandBtn64', 'collapseBtn64')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup64"><code class="language-tsx text-sm text-gray-800">, async () =&gt; {
    renderComponent();

    // Wait for projects to load
    await waitFor(() =&gt; {
      expect(apiClient.get).toHaveBeenCalledWith('/projects?limit=1000');
    });

    // Create a mock file
    const file = new File(['test'], 'test-image.jpg', { type: 'image/jpeg' });

    // Simulate file drop</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/SignIn.tsx (Line 87:7 - Line 104:4), packages/frontend/src/pages/SignUp.tsx (Line 119:5 - Line 136:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn87" onclick="toggleCodeBlock('cloneGroup87', 'expandBtn87', 'collapseBtn87')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn87" onclick="toggleCodeBlock('cloneGroup87', 'expandBtn87', 'collapseBtn87')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup87"><code class="language-tsx text-sm text-gray-800">&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    );
  }

  return (
    &lt;div className=&quot;relative min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 dark:from-gray-800 dark:via-gray-900 dark:to-black&quot;&gt;
      {/* Header Buttons - Positioned Absolutely */}
      &lt;div className=&quot;absolute top-4 left-4&quot;&gt;
        &lt;BackButton /&gt;
      &lt;/div&gt;
      &lt;div className=&quot;absolute top-4 right-4&quot;&gt;
        &lt;LanguageSwitcher /&gt;
      &lt;/div&gt;

      {/* Main Content Card - Centered */}
      &lt;div</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/SignIn.tsx (Line 186:14 - Line 194:39), packages/frontend/src/pages/SignUp.tsx (Line 301:14 - Line 309:58)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn88" onclick="toggleCodeBlock('cloneGroup88', 'expandBtn88', 'collapseBtn88')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn88" onclick="toggleCodeBlock('cloneGroup88', 'expandBtn88', 'collapseBtn88')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup88"><code class="language-tsx text-sm text-gray-800">)}
              &lt;/Button&gt;
            &lt;/Link&gt;
            &lt;Link to=&quot;/request-access&quot;&gt;
              &lt;Button variant=&quot;outline&quot; className=&quot;w-full h-10 text-base rounded-md&quot;&gt;
                {t('auth.requestAccess')}
              &lt;/Button&gt;
            &lt;/Link&gt;
            &lt;p className=&quot;text-center text-sm text-gray-600 mt-3</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/SignIn.tsx (Line 194:39 - Line 212:4), packages/frontend/src/pages/SignUp.tsx (Line 309:58 - Line 327:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn89" onclick="toggleCodeBlock('cloneGroup89', 'expandBtn89', 'collapseBtn89')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn89" onclick="toggleCodeBlock('cloneGroup89', 'expandBtn89', 'collapseBtn89')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup89"><code class="language-tsx text-sm text-gray-800">&quot;&gt;
              {t('auth.termsAndPrivacy')}{' '}
              &lt;Link
                to=&quot;/terms-of-service&quot;
                className=&quot;font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors&quot;
              &gt;
                {t('common.termsOfService')}
              &lt;/Link&gt;{' '}
              {t('requestAccess.and')}{' '}
              &lt;Link
                to=&quot;/privacy-policy&quot;
                className=&quot;font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors&quot;
              &gt;
                {t('common.privacyPolicy')}
              &lt;/Link&gt;
            &lt;/p&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/RequestAccess.tsx (Line 80:3 - Line 92:2), packages/frontend/src/pages/SignUp.tsx (Line 125:3 - Line 137:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn90" onclick="toggleCodeBlock('cloneGroup90', 'expandBtn90', 'collapseBtn90')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn90" onclick="toggleCodeBlock('cloneGroup90', 'expandBtn90', 'collapseBtn90')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup90"><code class="language-tsx text-sm text-gray-800">return (
    &lt;div className=&quot;relative min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 dark:from-gray-800 dark:via-gray-900 dark:to-black&quot;&gt;
      {/* Header Buttons - Positioned Absolutely */}
      &lt;div className=&quot;absolute top-4 left-4&quot;&gt;
        &lt;BackButton /&gt;
      &lt;/div&gt;
      &lt;div className=&quot;absolute top-4 right-4&quot;&gt;
        &lt;LanguageSwitcher /&gt;
      &lt;/div&gt;

      {/* Main Content Card - Centered */}
      &lt;Card className=&quot;w-full max-w-md shadow-xl bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-gray-200 dark:border-gray-700/50 rounded-lg overflow-hidden&quot;&gt;
        {</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/RequestAccess.tsx (Line 190:9 - Line 199:25), packages/frontend/src/pages/SignUp.tsx (Line 285:9 - Line 293:26)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn91" onclick="toggleCodeBlock('cloneGroup91', 'expandBtn91', 'collapseBtn91')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn91" onclick="toggleCodeBlock('cloneGroup91', 'expandBtn91', 'collapseBtn91')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup91"><code class="language-tsx text-sm text-gray-800">&lt;/CardContent&gt;

        &lt;div className=&quot;p-6 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-100 dark:border-gray-700/50&quot;&gt;
          &lt;div className=&quot;relative&quot;&gt;
            &lt;div className=&quot;absolute inset-0 flex items-center&quot;&gt;
              &lt;div className=&quot;w-full border-t border-gray-300 dark:border-gray-700&quot;&gt;&lt;/div&gt;
            &lt;/div&gt;
            &lt;div className=&quot;relative flex justify-center text-sm&quot;&gt;
              &lt;span className=&quot;px-4 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400&quot;&gt;
                {t('auth.alreadyHaveAccess'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/RequestAccess.tsx (Line 199:25 - Line 210:9), packages/frontend/src/pages/SignUp.tsx (Line 293:26 - Line 304:16)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn92" onclick="toggleCodeBlock('cloneGroup92', 'expandBtn92', 'collapseBtn92')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn92" onclick="toggleCodeBlock('cloneGroup92', 'expandBtn92', 'collapseBtn92')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup92"><code class="language-tsx text-sm text-gray-800">)}
              &lt;/span&gt;
            &lt;/div&gt;
          &lt;/div&gt;

          &lt;div className=&quot;mt-6 flex flex-col gap-3&quot;&gt;
            &lt;Link to=&quot;/sign-in&quot;&gt;
              &lt;Button variant=&quot;outline&quot; className=&quot;w-full h-10 text-base rounded-md&quot;&gt;
                {t('auth.signIn')}
              &lt;/Button&gt;
            &lt;/Link&gt;
            &lt;Link to=&quot;/sign-up</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/RequestAccess.tsx (Line 212:14 - Line 238:14), packages/frontend/src/pages/SignUp.tsx (Line 306:21 - Line 332:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn93" onclick="toggleCodeBlock('cloneGroup93', 'expandBtn93', 'collapseBtn93')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn93" onclick="toggleCodeBlock('cloneGroup93', 'expandBtn93', 'collapseBtn93')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup93"><code class="language-tsx text-sm text-gray-800">)}
              &lt;/Button&gt;
            &lt;/Link&gt;
            &lt;p className=&quot;text-center text-sm text-gray-600 dark:text-gray-400 mt-3&quot;&gt;
              {t('auth.termsAndPrivacy')}{' '}
              &lt;Link
                to=&quot;/terms-of-service&quot;
                className=&quot;font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors&quot;
              &gt;
                {t('common.termsOfService')}
              &lt;/Link&gt;{' '}
              {t('requestAccess.and')}{' '}
              &lt;Link
                to=&quot;/privacy-policy&quot;
                className=&quot;font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors&quot;
              &gt;
                {t('common.privacyPolicy')}
              &lt;/Link&gt;
            &lt;/p&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/Card&gt;
    &lt;/div&gt;
  );
};

export default RequestAccess</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/ProjectDetail.tsx (Line 510:2 - Line 520:3), packages/frontend/src/pages/ProjectDetail.tsx (Line 388:2 - Line 398:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn94" onclick="toggleCodeBlock('cloneGroup94', 'expandBtn94', 'collapseBtn94')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn94" onclick="toggleCodeBlock('cloneGroup94', 'expandBtn94', 'collapseBtn94')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup94"><code class="language-tsx text-sm text-gray-800">= () =&gt; {
    const selectedImageIds = Object.entries(selectedImages)
      .filter(([_, isSelected]) =&gt; isSelected)
      .map(([id]) =&gt; id);

    if (selectedImageIds.length === 0) {
      toast.error(t('project.detail.noImagesSelected'));
      return;
    }

    if</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/ProjectDetail.tsx (Line 577:2 - Line 587:33), packages/frontend/src/pages/ProjectDetail.tsx (Line 388:2 - Line 398:42)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn95" onclick="toggleCodeBlock('cloneGroup95', 'expandBtn95', 'collapseBtn95')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn95" onclick="toggleCodeBlock('cloneGroup95', 'expandBtn95', 'collapseBtn95')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup95"><code class="language-tsx text-sm text-gray-800">= () =&gt; {
    const selectedImageIds = Object.entries(selectedImages)
      .filter(([_, isSelected]) =&gt; isSelected)
      .map(([id]) =&gt; id);

    if (selectedImageIds.length === 0) {
      toast.error(t('project.detail.noImagesSelected'));
      return;
    }

    toast.info(t('project.detail.preparingExport'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/ProjectDetail.tsx (Line 646:2 - Line 664:10), packages/frontend/src/pages/ProjectDetail.tsx (Line 423:13 - Line 441:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn96" onclick="toggleCodeBlock('cloneGroup96', 'expandBtn96', 'collapseBtn96')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn96" onclick="toggleCodeBlock('cloneGroup96', 'expandBtn96', 'collapseBtn96')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup96"><code class="language-tsx text-sm text-gray-800">);
        }

        // Postupné zpracování všech dávek
        let successCount = 0;
        let failCount = 0;

        for (let i = 0; i &lt; batches.length; i++) {
            const batch = batches[i];
            console.log(`Zpracování dávky ${i+1}/${batches.length} s ${batch.length} obrázky`);

            // Přidáme krátké zpoždění mezi dávkami, aby se server nezahltil
            if (i &gt; 0) {
              await new Promise(resolve =&gt; setTimeout(resolve, 1000));
            }

            try {
                // Zkusit nejprve nový endpoint s validními parametry
                const response = await apiClient.post(`/api/projects/${projectId</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Profile.tsx (Line 20:1 - Line 32:10), packages/frontend/src/pages/Settings.tsx (Line 22:1 - Line 34:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn98" onclick="toggleCodeBlock('cloneGroup98', 'expandBtn98', 'collapseBtn98')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn98" onclick="toggleCodeBlock('cloneGroup98', 'expandBtn98', 'collapseBtn98')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup98"><code class="language-tsx text-sm text-gray-800">interface UserProfile {
  user_id: string;
  username: string | null;
  full_name: string | null;
  title: string | null;
  organization: string | null;
  bio: string | null;
  location: string | null;
  avatar_url: string | null;
  preferred_language: string | null;
}

interface</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Profile.tsx (Line 382:5 - Line 397:4), packages/frontend/src/pages/Profile.tsx (Line 362:8 - Line 377:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn99" onclick="toggleCodeBlock('cloneGroup99', 'expandBtn99', 'collapseBtn99')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn99" onclick="toggleCodeBlock('cloneGroup99', 'expandBtn99', 'collapseBtn99')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup99"><code class="language-tsx text-sm text-gray-800">) {
    return (
      &lt;div className=&quot;min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative flex justify-center items-center&quot;&gt;
        {/* Background elements */}
        &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
          &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
          &lt;div
            className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot;
            style={{ animationDelay: '-2s' }}
          /&gt;
          &lt;div
            className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot;
            style={{ animationDelay: '-4s' }}
          /&gt;
        &lt;/div&gt;
        &lt;div</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Profile.tsx (Line 402:12 - Line 417:82), packages/frontend/src/pages/Profile.tsx (Line 362:8 - Line 397:51)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn100" onclick="toggleCodeBlock('cloneGroup100', 'expandBtn100', 'collapseBtn100')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn100" onclick="toggleCodeBlock('cloneGroup100', 'expandBtn100', 'collapseBtn100')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup100"><code class="language-tsx text-sm text-gray-800">) {
    return (
      &lt;div className=&quot;min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative flex justify-center items-center&quot;&gt;
        {/* Background elements */}
        &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
          &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
          &lt;div
            className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot;
            style={{ animationDelay: '-2s' }}
          /&gt;
          &lt;div
            className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot;
            style={{ animationDelay: '-4s' }}
          /&gt;
        &lt;/div&gt;
        &lt;div className=&quot;p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md text-red-500 dark:text-red-400</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/PrivacyPolicy.tsx (Line 7:2 - Line 15:20), packages/frontend/src/pages/TermsOfService.tsx (Line 7:2 - Line 15:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn101" onclick="toggleCodeBlock('cloneGroup101', 'expandBtn101', 'collapseBtn101')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn101" onclick="toggleCodeBlock('cloneGroup101', 'expandBtn101', 'collapseBtn101')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup101"><code class="language-tsx text-sm text-gray-800">= () =&gt; {
  const { t } = useLanguage();

  return (
    &lt;div className=&quot;min-h-screen flex flex-col bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 dark:from-gray-800 dark:via-gray-900 dark:to-black&quot;&gt;
      &lt;Navbar /&gt;
      &lt;div className=&quot;container mx-auto px-4 py-12 flex-1 mt-16&quot;&gt;
        &lt;div className=&quot;max-w-4xl mx-auto&quot;&gt;
          &lt;h1 className=&quot;text-3xl font-bold mb-8&quot;&gt;{t('privacyPage.title'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/PrivacyPolicy.tsx (Line 51:2 - Line 59:18), packages/frontend/src/pages/TermsOfService.tsx (Line 36:5 - Line 44:16)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn102" onclick="toggleCodeBlock('cloneGroup102', 'expandBtn102', 'collapseBtn102')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn102" onclick="toggleCodeBlock('cloneGroup102', 'expandBtn102', 'collapseBtn102')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup102"><code class="language-tsx text-sm text-gray-800">&lt;/p&gt;
          &lt;/div&gt;

          &lt;div className=&quot;mt-8 flex justify-between&quot;&gt;
            &lt;Button variant=&quot;outline&quot; asChild&gt;
              &lt;Link to=&quot;/&quot;&gt;{t('common.backToHome')}&lt;/Link&gt;
            &lt;/Button&gt;
            &lt;Button asChild&gt;
              &lt;Link to=&quot;/terms-of-service</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Dashboard.tsx (Line 112:116 - Line 126:16), packages/frontend/src/pages/Profile.tsx (Line 364:133 - Line 377:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn103" onclick="toggleCodeBlock('cloneGroup103', 'expandBtn103', 'collapseBtn103')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn103" onclick="toggleCodeBlock('cloneGroup103', 'expandBtn103', 'collapseBtn103')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup103"><code class="language-tsx text-sm text-gray-800">&quot;&gt;
        {/* Background elements */}
        &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
          &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
          &lt;div
            className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot;
            style={{ animationDelay: '-2s' }}
          /&gt;
          &lt;div
            className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot;
            style={{ animationDelay: '-4s' }}
          /&gt;
        &lt;/div&gt;

        &lt;DashboardHeader</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Dashboard.tsx (Line 171:116 - Line 187:4), packages/frontend/src/pages/Profile.tsx (Line 430:114 - Line 445:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn104" onclick="toggleCodeBlock('cloneGroup104', 'expandBtn104', 'collapseBtn104')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn104" onclick="toggleCodeBlock('cloneGroup104', 'expandBtn104', 'collapseBtn104')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup104"><code class="language-tsx text-sm text-gray-800">&quot;&gt;
      {/* Background elements */}
      &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
        &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
        &lt;div
          className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot;
          style={{ animationDelay: '-2s' }}
        /&gt;
        &lt;div
          className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot;
          style={{ animationDelay: '-4s' }}
        /&gt;
      &lt;/div&gt;

      &lt;DashboardHeader /&gt;

      &lt;div</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useProjectData.tsx (Line 342:5 - Line 350:6), packages/frontend/src/pages/ProjectDetail.tsx (Line 277:5 - Line 285:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn110" onclick="toggleCodeBlock('cloneGroup110', 'expandBtn110', 'collapseBtn110')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn110" onclick="toggleCodeBlock('cloneGroup110', 'expandBtn110', 'collapseBtn110')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup110"><code class="language-tsx text-sm text-gray-800">const handleImageStatusUpdate = (event: Event) =&gt; {
      const customEvent = event as CustomEvent&lt;{
        imageId: string;
        status: ImageStatus;
        forceQueueUpdate?: boolean;
        error?: string;
        resultPath?: string | null;
      }&gt;;
      const { imageId, status, error</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/StatsOverview.tsx (Line 103:29 - Line 110:9), packages/frontend/src/components/project/ProjectImageProcessor.tsx (Line 45:32 - Line 52:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn118" onclick="toggleCodeBlock('cloneGroup118', 'expandBtn118', 'collapseBtn118')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn118" onclick="toggleCodeBlock('cloneGroup118', 'expandBtn118', 'collapseBtn118')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup118"><code class="language-tsx text-sm text-gray-800">;
        if (axios.isAxiosError(error) &amp;&amp; error.response) {
          message = error.response.data?.message || message;
        } else if (error instanceof Error) {
          message = error.message;
        }
        toast.error(message);
        setStats</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/SegmentationQueueIndicator.tsx (Line 230:9 - Line 240:6), packages/frontend/src/components/project/ImageCard.tsx (Line 73:13 - Line 83:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn119" onclick="toggleCodeBlock('cloneGroup119', 'expandBtn119', 'collapseBtn119')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn119" onclick="toggleCodeBlock('cloneGroup119', 'expandBtn119', 'collapseBtn119')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup119"><code class="language-tsx text-sm text-gray-800">const imageUpdateEvent = new CustomEvent('image-status-update', {
          detail: {
            imageId: data.imageId,
            status: data.status,
            forceQueueUpdate: true,
            error: data.error,
            resultPath: data.resultPath,
          },
        });
        window.dispatchEvent(imageUpdateEvent);
      } catch</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/NewProject.tsx (Line 86:9 - Line 97:17), packages/frontend/src/components/project/ProjectDialogForm.tsx (Line 21:7 - Line 32:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn121" onclick="toggleCodeBlock('cloneGroup121', 'expandBtn121', 'collapseBtn121')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn121" onclick="toggleCodeBlock('cloneGroup121', 'expandBtn121', 'collapseBtn121')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup121"><code class="language-tsx text-sm text-gray-800">&lt;DialogHeader&gt;
          &lt;DialogTitle&gt;{t('projects.createProject')}&lt;/DialogTitle&gt;
          &lt;DialogDescription&gt;{t('projects.createProjectDesc')}&lt;/DialogDescription&gt;
        &lt;/DialogHeader&gt;
        &lt;form onSubmit={handleCreateProject}&gt;
          &lt;div className=&quot;grid gap-4 py-4&quot;&gt;
            &lt;div className=&quot;space-y-2&quot;&gt;
              &lt;Label htmlFor=&quot;projectName&quot; className=&quot;text-right&quot;&gt;
                {t('common.projectName')}
              &lt;/Label&gt;
              &lt;Input
                </code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/Features.tsx (Line 69:5 - Line 92:2), packages/frontend/src/components/ThemedFooter.tsx (Line 50:7 - Line 73:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn123" onclick="toggleCodeBlock('cloneGroup123', 'expandBtn123', 'collapseBtn123')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn123" onclick="toggleCodeBlock('cloneGroup123', 'expandBtn123', 'collapseBtn123')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup123"><code class="language-tsx text-sm text-gray-800">// Navigate through the translation object to find the value
    let value = translations;
    for (const part of parts) {
      if (value &amp;&amp; typeof value === 'object' &amp;&amp; part in value) {
        value = value[part];
      } else {
        // If the key doesn't exist in the current language, fall back to English
        if (language !== 'en') {
          let englishValue = enTranslations;
          for (const p of parts) {
            if (englishValue &amp;&amp; typeof englishValue === 'object' &amp;&amp; p in englishValue) {
              englishValue = englishValue[p];
            } else {
              return key; // Key not found in English either
            }
          }
          return typeof englishValue === 'string' ? englishValue : key;
        }
        return key; // Key not found
      }
    }

    return typeof value === 'string' ? value : key;
  };</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/Features.tsx (Line 92:2 - Line 106:3), packages/frontend/src/components/Hero.tsx (Line 11:2 - Line 25:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn124" onclick="toggleCodeBlock('cloneGroup124', 'expandBtn124', 'collapseBtn124')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn124" onclick="toggleCodeBlock('cloneGroup124', 'expandBtn124', 'collapseBtn124')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup124"><code class="language-tsx text-sm text-gray-800">;

  useEffect(() =&gt; {
    const observer = new IntersectionObserver(
      (entries) =&gt; {
        entries.forEach((entry) =&gt; {
          if (entry.isIntersecting) {
            entry.target.classList.add('active');
          }
        });
      },
      { threshold: 0.1 },
    );

    if</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ErrorBoundary.tsx (Line 109:10 - Line 122:10), packages/frontend/src/pages/segmentation/SegmentationPage.tsx (Line 171:18 - Line 185:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn125" onclick="toggleCodeBlock('cloneGroup125', 'expandBtn125', 'collapseBtn125')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn125" onclick="toggleCodeBlock('cloneGroup125', 'expandBtn125', 'collapseBtn125')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup125"><code class="language-tsx text-sm text-gray-800">&quot;
              fill=&quot;none&quot;
              viewBox=&quot;0 0 24 24&quot;
              stroke=&quot;currentColor&quot;
            &gt;
              &lt;path
                strokeLinecap=&quot;round&quot;
                strokeLinejoin=&quot;round&quot;
                strokeWidth={2}
                d=&quot;M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z&quot;
              /&gt;
            &lt;/svg&gt;
          &lt;/div&gt;
          &lt;h2 className=&quot;text-2xl font-bold mb-2 dark:text-white&quot;&gt;Something</code></pre></div><!-- Add more clone groups for .txt format as needed         // Add more clone groups for .txt format as needed--></div><a name="javascript-clones"></a><h2 class="text-2xl font-semibold text-gray-700 mb-4">javascript</h2><div class="divide-y divide-gray-200 border-b-2"><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 314:10 - Line 340:10), packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 306:10 - Line 322:10)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn1" onclick="toggleCodeBlock('cloneGroup1', 'expandBtn1', 'collapseBtn1')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn1" onclick="toggleCodeBlock('cloneGroup1', 'expandBtn1', 'collapseBtn1')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup1"><code class="language-javascript text-sm text-gray-800">, marginBottom: '4px' }}&gt;Create Polygon Mode&lt;/div&gt;
              &lt;div&gt;1. Click to start creating a polygon&lt;/div&gt;
              &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;
                Hold SHIFT to automatically add points
              &lt;/div&gt;
            &lt;/div&gt;
          ) : tempPoints.length &lt; 3 ? (
            &lt;div&gt;
              &lt;div style={{ color: '#4ade80', marginBottom: '4px' }}&gt;Create Polygon Mode&lt;/div&gt;
              &lt;div&gt;2. Continue clicking to add more points (at least 3 needed)&lt;/div&gt;
              &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;
                Hold SHIFT to automatically add points • Press ESC to cancel
              &lt;/div&gt;
            &lt;/div&gt;
          ) : (
            &lt;div&gt;
              &lt;div style={{ color: '#4ade80', marginBottom: '4px' }}&gt;Create Polygon Mode&lt;/div&gt;
              &lt;div&gt;3. Continue adding points or click near the first point to close the polygon&lt;/div&gt;
              &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;
                Hold SHIFT to automatically add points • Press ESC to cancel
              &lt;/div&gt;
            &lt;/div&gt;
          )
        ) : editMode === EditMode.AddPoints ? (
          !interactionState?.isAddingPoints ? (
            &lt;div&gt;
              &lt;div style={{ color: '#60a5fa'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 340:10 - Line 356:10), packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 330:10 - Line 346:10)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn2" onclick="toggleCodeBlock('cloneGroup2', 'expandBtn2', 'collapseBtn2')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn2" onclick="toggleCodeBlock('cloneGroup2', 'expandBtn2', 'collapseBtn2')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup2"><code class="language-javascript text-sm text-gray-800">, marginBottom: '4px' }}&gt;Add Points Mode&lt;/div&gt;
              &lt;div&gt;Click on any vertex to start adding points&lt;/div&gt;
              &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;Press ESC to cancel&lt;/div&gt;
            &lt;/div&gt;
          ) : (
            &lt;div&gt;
              &lt;div style={{ color: '#60a5fa', marginBottom: '4px' }}&gt;Add Points Mode&lt;/div&gt;
              &lt;div&gt;Click to add points, then click on another vertex to complete&lt;/div&gt;
              &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;
                Hold SHIFT to automatically add points • Press ESC to cancel
              &lt;/div&gt;
            &lt;/div&gt;
          )
        ) : editMode === EditMode.EditVertices ? (
          selectedPolygonId ? (
            &lt;div&gt;
              &lt;div style={{ color: '#f97316'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 356:10 - Line 372:10), packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 346:10 - Line 364:10)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn3" onclick="toggleCodeBlock('cloneGroup3', 'expandBtn3', 'collapseBtn3')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn3" onclick="toggleCodeBlock('cloneGroup3', 'expandBtn3', 'collapseBtn3')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup3"><code class="language-javascript text-sm text-gray-800">, marginBottom: '4px' }}&gt;Edit Vertices Mode&lt;/div&gt;
              &lt;div&gt;Click and drag vertices to move them&lt;/div&gt;
              &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;
                Hold SHIFT and click a vertex to add points • Double-click a vertex to delete it
              &lt;/div&gt;
            &lt;/div&gt;
          ) : (
            &lt;div&gt;
              &lt;div style={{ color: '#f97316', marginBottom: '4px' }}&gt;Edit Vertices Mode&lt;/div&gt;
              &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;
                Click on a polygon to select it for editing
              &lt;/div&gt;
            &lt;/div&gt;
          )
        ) : editMode === EditMode.DeletePolygon ? (
          &lt;div&gt;
            &lt;div style={{ color: '#ef4444'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/SegmentationEditorV2.tsx (Line 199:9 - Line 216:10), packages/frontend/src/pages/segmentation/SegmentationPage.tsx (Line 211:9 - Line 229:12)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn33" onclick="toggleCodeBlock('cloneGroup33', 'expandBtn33', 'collapseBtn33')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn33" onclick="toggleCodeBlock('cloneGroup33', 'expandBtn33', 'collapseBtn33')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup33"><code class="language-javascript text-sm text-gray-800">={editMode}
        setEditMode={setEditMode}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onResetView={handleResetView}
        onSave={handleSave}
        onUndo={undo}
        onRedo={redo}
        onResegment={handleResegment}
        canUndo={canUndo}
        canRedo={canRedo}
        isSaving={isSaving}
        isResegmenting={isResegmenting}
      /&gt;

      &lt;div className=&quot;flex-1 relative&quot; id=&quot;canvas-container&quot;&gt;
        &lt;CanvasV2
          imageData={imageData</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ui/context-menu.tsx (Line 63:462 - Line 143:52), packages/frontend/src/components/ui/menubar.tsx (Line 91:409 - Line 171:36)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn40" onclick="toggleCodeBlock('cloneGroup40', 'expandBtn40', 'collapseBtn40')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn40" onclick="toggleCodeBlock('cloneGroup40', 'expandBtn40', 'collapseBtn40')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup40"><code class="language-javascript text-sm text-gray-800">,
        className,
      )}
      {...props}
    /&gt;
  &lt;/ContextMenuPrimitive.Portal&gt;
));
ContextMenuContent.displayName = ContextMenuPrimitive.Content.displayName;

const ContextMenuItem = React.forwardRef&lt;
  React.ElementRef&lt;typeof ContextMenuPrimitive.Item&gt;,
  React.ComponentPropsWithoutRef&lt;typeof ContextMenuPrimitive.Item&gt; &amp; {
    inset?: boolean;
  }
&gt;(({ className, inset, ...props }, ref) =&gt; (
  &lt;ContextMenuPrimitive.Item
    ref={ref}
    className={cn(
      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      inset &amp;&amp; 'pl-8',
      className,
    )}
    {...props}
  /&gt;
));
ContextMenuItem.displayName = ContextMenuPrimitive.Item.displayName;

const ContextMenuCheckboxItem = React.forwardRef&lt;
  React.ElementRef&lt;typeof ContextMenuPrimitive.CheckboxItem&gt;,
  React.ComponentPropsWithoutRef&lt;typeof ContextMenuPrimitive.CheckboxItem&gt;
&gt;(({ className, children, checked, ...props }, ref) =&gt; (
  &lt;ContextMenuPrimitive.CheckboxItem
    ref={ref}
    className={cn(
      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      className,
    )}
    checked={checked}
    {...props}
  &gt;
    &lt;span className=&quot;absolute left-2 flex h-3.5 w-3.5 items-center justify-center&quot;&gt;
      &lt;ContextMenuPrimitive.ItemIndicator&gt;
        &lt;Check className=&quot;h-4 w-4&quot; /&gt;
      &lt;/ContextMenuPrimitive.ItemIndicator&gt;
    &lt;/span&gt;
    {children}
  &lt;/ContextMenuPrimitive.CheckboxItem&gt;
));
ContextMenuCheckboxItem.displayName = ContextMenuPrimitive.CheckboxItem.displayName;

const ContextMenuRadioItem = React.forwardRef&lt;
  React.ElementRef&lt;typeof ContextMenuPrimitive.RadioItem&gt;,
  React.ComponentPropsWithoutRef&lt;typeof ContextMenuPrimitive.RadioItem&gt;
&gt;(({ className, children, ...props }, ref) =&gt; (
  &lt;ContextMenuPrimitive.RadioItem
    ref={ref}
    className={cn(
      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      className,
    )}
    {...props}
  &gt;
    &lt;span className=&quot;absolute left-2 flex h-3.5 w-3.5 items-center justify-center&quot;&gt;
      &lt;ContextMenuPrimitive.ItemIndicator&gt;
        &lt;Circle className=&quot;h-2 w-2 fill-current&quot; /&gt;
      &lt;/ContextMenuPrimitive.ItemIndicator&gt;
    &lt;/span&gt;
    {children}
  &lt;/ContextMenuPrimitive.RadioItem&gt;
));
ContextMenuRadioItem.displayName = ContextMenuPrimitive.RadioItem.displayName;

const ContextMenuLabel = React.forwardRef&lt;
  React.ElementRef&lt;typeof ContextMenuPrimitive.Label&gt;,
  React.ComponentPropsWithoutRef&lt;typeof ContextMenuPrimitive.Label&gt; &amp; {
    inset?: boolean;
  }
&gt;(({ className, inset, ...props }, ref) =&gt; (
  &lt;ContextMenuPrimitive.Label
    ref={ref}
    className={cn('px-2 py-1.5 text-sm font-semibold text-foreground'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/settings/UserProfileSection.tsx (Line 181:30 - Line 193:16), packages/frontend/src/components/settings/UserProfileSection.tsx (Line 167:30 - Line 179:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn41" onclick="toggleCodeBlock('cloneGroup41', 'expandBtn41', 'collapseBtn41')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn41" onclick="toggleCodeBlock('cloneGroup41', 'expandBtn41', 'collapseBtn41')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup41"><code class="language-javascript text-sm text-gray-800">)} {...field} value={field.value ?? ''} /&gt;
                  &lt;/FormControl&gt;
                  &lt;FormMessage /&gt;
                &lt;/FormItem&gt;
              )}
            /&gt;
            {/* Title Field */}
            &lt;FormField
              control={form.control}
              name=&quot;title&quot;
              render={({ field }) =&gt; (
                &lt;FormItem&gt;
                  &lt;FormLabel&gt;{t('profile.title'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/settings/UserProfileSection.tsx (Line 195:27 - Line 207:23), packages/frontend/src/components/settings/UserProfileSection.tsx (Line 167:30 - Line 179:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn42" onclick="toggleCodeBlock('cloneGroup42', 'expandBtn42', 'collapseBtn42')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn42" onclick="toggleCodeBlock('cloneGroup42', 'expandBtn42', 'collapseBtn42')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup42"><code class="language-javascript text-sm text-gray-800">)} {...field} value={field.value ?? ''} /&gt;
                  &lt;/FormControl&gt;
                  &lt;FormMessage /&gt;
                &lt;/FormItem&gt;
              )}
            /&gt;
            {/* Organization Field */}
            &lt;FormField
              control={form.control}
              name=&quot;organization&quot;
              render={({ field }) =&gt; (
                &lt;FormItem&gt;
                  &lt;FormLabel&gt;{t('profile.organization'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/settings/UserProfileSection.tsx (Line 209:34 - Line 221:14), packages/frontend/src/components/settings/UserProfileSection.tsx (Line 167:30 - Line 179:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn43" onclick="toggleCodeBlock('cloneGroup43', 'expandBtn43', 'collapseBtn43')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn43" onclick="toggleCodeBlock('cloneGroup43', 'expandBtn43', 'collapseBtn43')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup43"><code class="language-javascript text-sm text-gray-800">)} {...field} value={field.value ?? ''} /&gt;
                  &lt;/FormControl&gt;
                  &lt;FormMessage /&gt;
                &lt;/FormItem&gt;
              )}
            /&gt;
            {/* Bio Field */}
            &lt;FormField
              control={form.control}
              name=&quot;bio&quot;
              render={({ field }) =&gt; (
                &lt;FormItem&gt;
                  &lt;FormLabel&gt;{t('profile.bio'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ImageCard.tsx (Line 335:2 - Line 365:2), packages/frontend/src/components/project/ImageListItem.tsx (Line 242:6 - Line 259:13)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn57" onclick="toggleCodeBlock('cloneGroup57', 'expandBtn57', 'collapseBtn57')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn57" onclick="toggleCodeBlock('cloneGroup57', 'expandBtn57', 'collapseBtn57')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup57"><code class="language-javascript text-sm text-gray-800">}
      layout
    &gt;
      &lt;Card
        className={cn(
          'overflow-hidden border-gray-200 dark:border-gray-700 transition-all group hover:shadow-md relative',
          isSelected ? 'ring-2 ring-blue-500' : '',
          className,
        )}
        onClick={(e) =&gt; {
          if (selectionMode) {
            onToggleSelection?.(e);
          } else if (onOpen) {
            onOpen(image.id);
          }
        }}
      &gt;
        {/* Selection checkbox or actions */}
        {selectionMode ? (
          &lt;div className=&quot;absolute top-2 right-2 z-10&quot;&gt;
            &lt;div onClick={(e) =&gt; e.stopPropagation()}&gt;
              &lt;input
                type=&quot;checkbox&quot;
                checked={isSelected}
                onChange={(e) =&gt; onToggleSelection?.(e.nativeEvent)}
                className=&quot;h-5 w-5 rounded border-gray-300&quot;
              /&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        ) : (
          &lt;ImageActions onDelete={() =&gt; onDelete(image.id)} onResegment={() =&gt; </code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/SignUp.tsx (Line 211:27 - Line 225:25), packages/frontend/src/pages/SignUp.tsx (Line 192:24 - Line 206:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn85" onclick="toggleCodeBlock('cloneGroup85', 'expandBtn85', 'collapseBtn85')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn85" onclick="toggleCodeBlock('cloneGroup85', 'expandBtn85', 'collapseBtn85')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup85"><code class="language-javascript text-sm text-gray-800">)}
                        {...field}
                        className=&quot;h-10 bg-gray-50 dark:bg-gray-700/50 border-gray-300 dark:border-gray-600 rounded-md&quot;
                      /&gt;
                    &lt;/FormControl&gt;
                    &lt;FormMessage /&gt;
                  &lt;/FormItem&gt;
                )}
              /&gt;
              &lt;FormField
                control={form.control}
                name=&quot;confirmPassword&quot;
                render={({ field }) =&gt; (
                  &lt;FormItem&gt;
                    &lt;FormLabel&gt;{t('common.passwordConfirm'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/SignUp.tsx (Line 230:2 - Line 243:2), packages/frontend/src/pages/SignUp.tsx (Line 211:2 - Line 205:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn86" onclick="toggleCodeBlock('cloneGroup86', 'expandBtn86', 'collapseBtn86')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn86" onclick="toggleCodeBlock('cloneGroup86', 'expandBtn86', 'collapseBtn86')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup86"><code class="language-javascript text-sm text-gray-800">t('auth.passwordPlaceholder')}
                        {...field}
                        className=&quot;h-10 bg-gray-50 dark:bg-gray-700/50 border-gray-300 dark:border-gray-600 rounded-md&quot;
                      /&gt;
                    &lt;/FormControl&gt;
                    &lt;FormMessage /&gt;
                  &lt;/FormItem&gt;
                )}
              /&gt;
              &lt;FormField
                control={form.control}
                name=&quot;agreeTerms&quot;
                render={({ field }) =&gt; (
                  &lt;FormItem </code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Profile.tsx (Line 390:6 - Line 454:11), packages/frontend/src/pages/Profile.tsx (Line 370:6 - Line 436:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn97" onclick="toggleCodeBlock('cloneGroup97', 'expandBtn97', 'collapseBtn97')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn97" onclick="toggleCodeBlock('cloneGroup97', 'expandBtn97', 'collapseBtn97')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup97"><code class="language-javascript text-sm text-gray-800">={{ animationDelay: '-2s' }}
          /&gt;
          &lt;div
            className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot;
            style={{ animationDelay: '-4s' }}
          /&gt;
        &lt;/div&gt;
        &lt;div className=&quot;p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md&quot;&gt;{t('common.pleaseLogin')}&lt;/div&gt;
      &lt;/div&gt;
    );
  }

  if (!profileData) {
    return (
      &lt;div className=&quot;min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative flex justify-center items-center&quot;&gt;
        {/* Background elements */}
        &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
          &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
          &lt;div
            className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot;
            style={{ animationDelay: '-2s' }}
          /&gt;
          &lt;div
            className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot;
            style={{ animationDelay: '-4s' }}
          /&gt;
        &lt;/div&gt;
        &lt;div className=&quot;p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md text-red-500 dark:text-red-400&quot;&gt;
          {t('profile.fetchError')}
        &lt;/div&gt;
      &lt;/div&gt;
    );
  }

  const joinedDate = user.created_at ? new Date(user.created_at) : new Date();
  const month = joinedDate.toLocaleString('default', { month: 'long' });
  const year = joinedDate.getFullYear();
  const formattedJoinedDate = `${month} ${year}`;

  return (
    &lt;div className=&quot;flex flex-col min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative&quot;&gt;
      {/* Background elements */}
      &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
        &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
        &lt;div
          className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot;
          style={{ animationDelay: '-2s' }}
        /&gt;
        &lt;div
          className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot;
          style={{ animationDelay: '-4s' }}
        /&gt;
      &lt;/div&gt;

      &lt;DashboardHeader /&gt;
      &lt;main className=&quot;flex-1 p-4 md:p-6 lg:p-8 relative z-10&quot;&gt;
        &lt;h1 className=&quot;text-2xl font-bold mb-6&quot;&gt;{t('profile.pageTitle')}&lt;/h1&gt;
        &lt;div className=&quot;grid gap-8 lg:grid-cols-3&quot;&gt;
          &lt;Card className=&quot;lg:col-span-1&quot;&gt;
            &lt;CardContent className=&quot;p-6 text-center&quot;&gt;
              &lt;div className=&quot;relative w-24 h-24 mx-auto mb-4&quot;&gt;
                &lt;Avatar className=&quot;w-24 h-24 border-2 border-white dark:border-gray-800 shadow-md&quot;&gt;
                  {avatarUrl || profileData.avatar_url ? (
                    &lt;AvatarImage
                      src={avatarUrl </code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ThemedFooter.tsx (Line 140:2 - Line 211:11), packages/frontend/src/components/ThemedFooter.tsx (Line 134:16 - Line 202:20)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn116" onclick="toggleCodeBlock('cloneGroup116', 'expandBtn116', 'collapseBtn116')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn116" onclick="toggleCodeBlock('cloneGroup116', 'expandBtn116', 'collapseBtn116')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup116"><code class="language-javascript text-sm text-gray-800">}
                aria-label=&quot;GitHub Repository&quot;
              &gt;
                &lt;Github className=&quot;w-5 h-5&quot; /&gt;
              &lt;/a&gt;
              &lt;a href=&quot;mailto:<EMAIL>&quot; className={getLinkHoverClasses()} aria-label=&quot;Contact Email&quot;&gt;
                &lt;Mail className=&quot;w-5 h-5&quot; /&gt;
              &lt;/a&gt;
            &lt;/div&gt;
          &lt;/div&gt;

          {/* Information Section */}
          &lt;div&gt;
            &lt;h3 className=&quot;text-lg font-semibold mb-6&quot;&gt;{getTranslation('footer.informationTitle')}&lt;/h3&gt;
            &lt;ul className=&quot;space-y-4&quot;&gt;
              &lt;li&gt;
                &lt;Link to=&quot;/documentation&quot; className={getLinkHoverClasses()}&gt;
                  {getTranslation('footer.documentationLink')}
                &lt;/Link&gt;
              &lt;/li&gt;
              &lt;li&gt;
                &lt;Link to=&quot;/terms-of-service&quot; className={getLinkHoverClasses()}&gt;
                  {getTranslation('footer.termsLink')}
                &lt;/Link&gt;
              &lt;/li&gt;
              &lt;li&gt;
                &lt;Link to=&quot;/privacy-policy&quot; className={getLinkHoverClasses()}&gt;
                  {getTranslation('footer.privacyLink')}
                &lt;/Link&gt;
              &lt;/li&gt;
              &lt;li&gt;
                &lt;Link to=&quot;/request-access&quot; className={getLinkHoverClasses()}&gt;
                  {getTranslation('footer.requestAccessLink') || 'Request Access'}
                &lt;/Link&gt;
              &lt;/li&gt;
            &lt;/ul&gt;
          &lt;/div&gt;

          {/* Contact Section */}
          &lt;div&gt;
            &lt;h3 className=&quot;text-lg font-semibold mb-6&quot;&gt;{getTranslation('footer.contactTitle')}&lt;/h3&gt;
            &lt;ul className=&quot;space-y-4&quot;&gt;
              &lt;li&gt;
                &lt;a href=&quot;mailto:<EMAIL>&quot; className={getLinkHoverClasses()}&gt;
                  <EMAIL>
                &lt;/a&gt;
              &lt;/li&gt;
              &lt;li&gt;
                &lt;a
                  href=&quot;https://www.fjfi.cvut.cz/&quot;
                  target=&quot;_blank&quot;
                  rel=&quot;noopener noreferrer&quot;
                  className={getLinkHoverClasses()}
                &gt;
                  FNSPE CTU in Prague
                &lt;/a&gt;
              &lt;/li&gt;
              &lt;li&gt;
                &lt;a
                  href=&quot;https://www.utia.cas.cz/&quot;
                  target=&quot;_blank&quot;
                  rel=&quot;noopener noreferrer&quot;
                  className={getLinkHoverClasses()}
                &gt;
                  UTIA CAS
                &lt;/a&gt;
              &lt;/li&gt;
            &lt;/ul&gt;
          &lt;/div&gt;
        &lt;/div&gt;

        &lt;div className={`border-t </code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ThemeToggle.tsx (Line 47:2 - Line 81:2), packages/frontend/src/components/settings/AppearanceSection.tsx (Line 59:6 - Line 98:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn117" onclick="toggleCodeBlock('cloneGroup117', 'expandBtn117', 'collapseBtn117')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn117" onclick="toggleCodeBlock('cloneGroup117', 'expandBtn117', 'collapseBtn117')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup117"><code class="language-javascript text-sm text-gray-800">}&gt;
            &lt;MonitorSmartphone className=&quot;h-4 w-4 mr-2&quot; /&gt;
            {t('settings.system')}
          &lt;/DropdownMenuItem&gt;
        &lt;/DropdownMenuContent&gt;
      &lt;/DropdownMenu&gt;
    );
  }

  // Default variant shows three buttons
  return (
    &lt;div className=&quot;flex items-center space-x-2&quot;&gt;
      &lt;Button
        variant={theme === 'light' ? 'default' : 'outline'}
        size=&quot;sm&quot;
        className=&quot;w-24&quot;
        onClick={() =&gt; handleThemeChange('light')}
      &gt;
        &lt;Sun className=&quot;h-4 w-4 mr-2&quot; /&gt;
        {t('settings.light')}
      &lt;/Button&gt;
      &lt;Button
        variant={theme === 'dark' ? 'default' : 'outline'}
        size=&quot;sm&quot;
        className=&quot;w-24&quot;
        onClick={() =&gt; handleThemeChange('dark')}
      &gt;
        &lt;Moon className=&quot;h-4 w-4 mr-2&quot; /&gt;
        {t('settings.dark')}
      &lt;/Button&gt;
      &lt;Button
        variant={theme === 'system' ? 'default' : 'outline'}
        size=&quot;sm&quot;
        className=&quot;w-24&quot;
        onClick={() =&gt; handleThemeChange('system')}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/NewProject.tsx (Line 90:20 - Line 112:2), packages/frontend/src/components/project/ProjectDialogForm.tsx (Line 25:2 - Line 49:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn120" onclick="toggleCodeBlock('cloneGroup120', 'expandBtn120', 'collapseBtn120')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn120" onclick="toggleCodeBlock('cloneGroup120', 'expandBtn120', 'collapseBtn120')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup120"><code class="language-javascript text-sm text-gray-800">}&gt;
          &lt;div className=&quot;grid gap-4 py-4&quot;&gt;
            &lt;div className=&quot;space-y-2&quot;&gt;
              &lt;Label htmlFor=&quot;projectName&quot; className=&quot;text-right&quot;&gt;
                {t('common.projectName')}
              &lt;/Label&gt;
              &lt;Input
                id=&quot;projectName&quot;
                placeholder={t('projects.projectNamePlaceholder')}
                value={projectName}
                onChange={(e) =&gt; setProjectName(e.target.value)}
                required
              /&gt;
            &lt;/div&gt;
            &lt;div className=&quot;space-y-2&quot;&gt;
              &lt;Label htmlFor=&quot;projectDescription&quot; className=&quot;text-right&quot;&gt;
                {t('common.description')} ({t('common.optional')})
              &lt;/Label&gt;
              &lt;Input
                id=&quot;projectDescription&quot;
                placeholder={t('projects.projectDescPlaceholder')}
                value={projectDescription}
                onChange={(e) =&gt; setProjectDescription(e.target.value)}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/Navbar.tsx (Line 115:2 - Line 148:2), packages/frontend/src/components/Navbar.tsx (Line 103:3 - Line 143:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn122" onclick="toggleCodeBlock('cloneGroup122', 'expandBtn122', 'collapseBtn122')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn122" onclick="toggleCodeBlock('cloneGroup122', 'expandBtn122', 'collapseBtn122')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup122"><code class="language-javascript text-sm text-gray-800">}
            &gt;
              {t('navbar.home')}
            &lt;/Link&gt;
            &lt;Link
              to=&quot;/documentation&quot;
              className=&quot;text-gray-700 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 py-2 transition-colors&quot;
              onClick={() =&gt; setIsMobileMenuOpen(false)}
            &gt;
              {t('navbar.documentation')}
            &lt;/Link&gt;
            &lt;Link
              to=&quot;/terms-of-service&quot;
              className=&quot;text-gray-700 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 py-2 transition-colors&quot;
              onClick={() =&gt; setIsMobileMenuOpen(false)}
            &gt;
              {t('navbar.terms')}
            &lt;/Link&gt;
            &lt;Link
              to=&quot;/privacy-policy&quot;
              className=&quot;text-gray-700 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 py-2 transition-colors&quot;
              onClick={() =&gt; setIsMobileMenuOpen(false)}
            &gt;
              {t('navbar.privacy')}
            &lt;/Link&gt;
            &lt;Link
              to=&quot;/sign-in&quot;
              className=&quot;text-gray-700 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 py-2 transition-colors&quot;
              onClick={() =&gt; setIsMobileMenuOpen(false)}
            &gt;
              {t('navbar.login')}
            &lt;/Link&gt;
            &lt;Button asChild className=&quot;w-full rounded-md&quot;&gt;
              &lt;Link to=&quot;/request-access&quot; onClick={() =&gt; setIsMobileMenuOpen(false)}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/DashboardActions.tsx (Line 27:8 - Line 38:2), packages/frontend/src/components/project/ProjectToolbar.tsx (Line 208:8 - Line 234:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn126" onclick="toggleCodeBlock('cloneGroup126', 'expandBtn126', 'collapseBtn126')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn126" onclick="toggleCodeBlock('cloneGroup126', 'expandBtn126', 'collapseBtn126')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup126"><code class="language-javascript text-sm text-gray-800">={viewMode === 'grid' ? 'default' : 'ghost'}
          size=&quot;sm&quot;
          className=&quot;h-9 px-2.5 rounded-r-none&quot;
          onClick={() =&gt; setViewMode('grid')}
        &gt;
          &lt;Grid2X2 className=&quot;h-4 w-4&quot; /&gt;
        &lt;/Button&gt;
        &lt;Button
          variant={viewMode === 'list' ? 'default' : 'ghost'}
          size=&quot;sm&quot;
          className=&quot;h-9 px-2.5 rounded-l-none&quot;
          onClick={() =&gt; setViewMode('list')}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/scripts/test-api-routes.js (Line 117:5 - Line 131:17), packages/backend/src/scripts/test-api-routes.js (Line 76:5 - Line 90:39)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn140" onclick="toggleCodeBlock('cloneGroup140', 'expandBtn140', 'collapseBtn140')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn140" onclick="toggleCodeBlock('cloneGroup140', 'expandBtn140', 'collapseBtn140')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup140"><code class="language-javascript text-sm text-gray-800">};
  } catch (error) {
    console.log('  Result: ERROR');
    console.log('  Error:', error.message);
    console.log('');
    
    return {
      route,
      success: false,
      error: error.message
    };
  }
}

// Main function</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/App.tsx (Line 352:14 - Line 372:17), packages/frontend/src/App.tsx (Line 341:10 - Line 362:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn189" onclick="toggleCodeBlock('cloneGroup189', 'expandBtn189', 'collapseBtn189')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn189" onclick="toggleCodeBlock('cloneGroup189', 'expandBtn189', 'collapseBtn189')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup189"><code class="language-javascript text-sm text-gray-800"> /&gt;
          &lt;/ErrorBoundary&gt;
        &lt;/ProtectedRoute&gt;
      }
    /&gt;
    {/* Ensure both URL formats work properly */}
    &lt;Route
      path=&quot;/projects/:id&quot;
      element={
        &lt;ProtectedRoute&gt;
          &lt;ErrorBoundary componentName=&quot;ProjectDetailPage&quot;&gt;
            &lt;ProjectDetail /&gt;
          &lt;/ErrorBoundary&gt;
        &lt;/ProtectedRoute&gt;
      }
    /&gt;
    &lt;Route
      path=&quot;/projects/:projectId/segmentation/:imageId&quot;
      element={
        &lt;ProtectedRoute&gt;
          &lt;ErrorBoundary componentName=&quot;SegmentationPage</code></pre></div><!-- Add more clone groups for .txt format as needed         // Add more clone groups for .txt format as needed--></div><a name="css-clones"></a><h2 class="text-2xl font-semibold text-gray-700 mb-4">css</h2><div class="divide-y divide-gray-200 border-b-2"><!-- Add more clone groups for .txt format as needed         // Add more clone groups for .txt format as needed--></div></section><!-- Add more sections for other formats and clone groups as needed--></main><footer class="bg-white shadow mt-8 py-4"><div class="container mx-auto px-4 text-center"><p class="text-sm text-gray-600">This report is generated by jscpd, an open-source copy/paste detector.</p><p class="text-sm text-gray-600">jscpd is licensed under the MIT License.</p><a class="text-blue-500 text-sm" href="https://github.com/kucherenko/jscpd" target="_blank" rel="noopener noreferrer">View jscpd on GitHub</a></div></footer><script src="js/prism.js"></script><script>function toggleCodeBlock(codeBlockId, expandBtnId, collapseBtnId) {
  const codeBlock = document.getElementById(codeBlockId);
  const expandBtn = document.getElementById(expandBtnId);
  const collapseBtn = document.getElementById(collapseBtnId);

  codeBlock.classList.toggle('hidden');
  expandBtn.classList.toggle('hidden');
  collapseBtn.classList.toggle('hidden');
}</script></body></html>